<form [formGroup]="contractForm" class="example-form">
  <div class="row">
    <mat-form-field class="example-half-width" appearance="outline">
      <mat-label>Número de Contrato</mat-label>
      <mat-icon matPrefix>tag</mat-icon>
      <input
        matInput
        formControlName="contractNumber"
        type="number"
        title="Número de Contrato"
        placeholder="Ingrese número de contrato"
      />
      @if (contractForm.get("contractNumber")?.hasError("required")) {
        <mat-error>
          <mat-icon>error_outline</mat-icon>
          Este campo es obligatorio
        </mat-error>
      }
      @if (contractForm.get("contractNumber")?.hasError("min")) {
        <mat-error>
          <mat-icon>error_outline</mat-icon>
          El número de contrato debe ser mayor que 0
        </mat-error>
      }
      @if (contractForm.get("contractNumber")?.hasError("duplicateContract")) {
        <mat-error>
          <mat-icon>error_outline</mat-icon>
          Este número de contrato ya existe para el año seleccionado
        </mat-error>
      }
    </mat-form-field>

    <mat-form-field class="example-half-width" appearance="outline">
      <mat-label>Año del Contrato</mat-label>
      <mat-icon matPrefix>calendar_today</mat-icon>
      <mat-select formControlName="contractYear">
        <mat-option [value]="null">Seleccione Año</mat-option>
        @for (year of contractYears; track year) {
          <mat-option [value]="year.id">{{ year.year }}</mat-option>
        }
      </mat-select>
      @if (contractForm.get("contractYear")?.hasError("required")) {
        <mat-error>
          <mat-icon>error_outline</mat-icon>
          Este campo es obligatorio
        </mat-error>
      }
    </mat-form-field>
  </div>

  <div class="row">
    <mat-form-field class="example-full-width" appearance="outline">
      <mat-label>Objeto del Contrato</mat-label>
      <mat-icon matPrefix>description</mat-icon>
      <textarea
        matInput
        formControlName="object"
        placeholder="Describa el objeto del contrato..."
        class="full-width"
        rows="5"
      ></textarea>
      @if (contractForm.get("object")?.hasError("required")) {
        <mat-error>
          <mat-icon>error_outline</mat-icon>
          Este campo es obligatorio
        </mat-error>
      }
    </mat-form-field>
  </div>

  <div class="row">
    <mat-form-field class="example-full-width" appearance="outline">
      <mat-label>Enlace SECOP</mat-label>
      <mat-icon matPrefix>link</mat-icon>
      <input
        matInput
        formControlName="secopLink"
        title="Enlace SECOP"
        placeholder="Ingrese enlace SECOP"
      />
    </mat-form-field>
  </div>

  <div class="row">
    <mat-form-field class="example-half-width" appearance="outline">
      <mat-label>Codigo SECOP</mat-label>
      <mat-icon matPrefix>code</mat-icon>
      <input
        matInput
        formControlName="secopCode"
        type="number"
        required
        title="Código SECOP"
        placeholder="Ingrese código SECOP"
      />
      @if (contractForm.get("secopCode")?.hasError("required")) {
        <mat-error>
          <mat-icon>error_outline</mat-icon>
          Este campo es obligatorio
        </mat-error>
      }
    </mat-form-field>
    <mat-form-field class="example-half-width" appearance="outline">
      <mat-label>Seleccione Modalidad</mat-label>
      <mat-icon matPrefix>category</mat-icon>
      <mat-select formControlName="selectionModality">
        <mat-option [value]="null">Seleccione Modalidad</mat-option>
        @for (modality of selectionModalities; track modality) {
          <mat-option [value]="modality.id">{{ modality.name }}</mat-option>
        }
      </mat-select>
      @if (contractForm.get("selectionModality")?.hasError("required")) {
        <mat-error>
          <mat-icon>error_outline</mat-icon>
          Este campo es obligatorio
        </mat-error>
      }
    </mat-form-field>
  </div>
  <div class="row">
    <mat-form-field class="example-full-width" appearance="outline">
      <mat-label
        >Seleccione causales dentro de la modalidad de selección</mat-label
      >
      <mat-icon matPrefix>category</mat-icon>
      <mat-select formControlName="causesSelectionId">
        <mat-option [value]="null"
          >Seleccione causales dentro de la modalidad de selección</mat-option
        >
        @for (causes of causesSelection; track causes) {
          <mat-option [value]="causes.id">{{ causes.name }}</mat-option>
        }
      </mat-select>
      @if (contractForm.get("causesSelectionId")?.hasError("required")) {
        <mat-error>
          <mat-icon>error_outline</mat-icon>
          Este campo es obligatorio
        </mat-error>
      }
    </mat-form-field>
  </div>
  <div class="row">
    <mat-form-field class="example-half-width" appearance="outline">
      <mat-label>Seleccione Clase De Contrato</mat-label>
      <mat-icon matPrefix>category</mat-icon>
      <mat-select formControlName="contractClassId">
        <mat-option [value]="null">Seleccione Clase De Contrato</mat-option>
        @for (contractclass of contractClass; track contractclass) {
          <mat-option [value]="contractclass.id">{{
            contractclass.name
          }}</mat-option>
        }
      </mat-select>
      @if (contractForm.get("contractClassId")?.hasError("required")) {
        <mat-error>
          <mat-icon>error_outline</mat-icon>
          Este campo es obligatorio
        </mat-error>
      }
    </mat-form-field>
    <mat-form-field class="example-half-width" appearance="outline">
      <mat-label>Seleccione apoyo a la gestion</mat-label>
      <mat-icon matPrefix>category</mat-icon>
      <mat-select formControlName="managementSupportId">
        <mat-option [value]="null">Selecione apoyo a la gestion</mat-option>
        @for (managementsupport of managementSupport; track managementsupport) {
          <mat-option [value]="managementsupport.id">{{
            managementsupport.name
          }}</mat-option>
        }
      </mat-select>
      @if (contractForm.get("managementSupportId")?.hasError("required")) {
        <mat-error>
          <mat-icon>error_outline</mat-icon>
          Este campo es obligatorio
        </mat-error>
      }
    </mat-form-field>
  </div>
  <div class="row">
    <mat-form-field class="example-half-width" appearance="outline">
      <mat-label>Seleccione Tipo de seguimiento</mat-label>
      <mat-icon matPrefix>track_changes</mat-icon>
      <mat-select formControlName="trackingType">
        <mat-option [value]="null">Seleccione Tipo de seguimiento</mat-option>
        @for (type of trackingTypes; track type) {
          <mat-option [value]="type.id">{{ type.name }}</mat-option>
        }
      </mat-select>
      @if (contractForm.get("trackingType")?.hasError("required")) {
        <mat-error>
          <mat-icon>error_outline</mat-icon>
          Este campo es obligatorio
        </mat-error>
      }
    </mat-form-field>
    <mat-form-field class="example-half-width" appearance="outline">
      <mat-label>Seleccione Tipo de contrato</mat-label>
      <mat-icon matPrefix>description</mat-icon>
      <mat-select formControlName="contractType">
        <mat-option [value]="null">Seleccione Tipo de contrato</mat-option>
        @for (type of contractTypes; track type) {
          <mat-option [value]="type.id">{{ type.name }}</mat-option>
        }
      </mat-select>
      @if (contractForm.get("contractType")?.hasError("required")) {
        <mat-error>
          <mat-icon>error_outline</mat-icon>
          Este campo es obligatorio
        </mat-error>
      }
    </mat-form-field>
  </div>
  <div class="row">
    <mat-form-field class="example-full-width" appearance="outline">
      <mat-label>Enlace SIGEP</mat-label>
      <mat-icon matPrefix>link</mat-icon>
      <input
        matInput
        formControlName="sigepLink"
        title="Enlace SIGEP"
        placeholder="Ingrese enlace SIGEP"
      />
    </mat-form-field>
  </div>
  <div class="row">
    <mat-form-field class="example-half-width" appearance="outline">
      <mat-label>Seleccione Dependencia</mat-label>
      <mat-icon matPrefix>group</mat-icon>
      <mat-select
        formControlName="dependency"
        (selectionChange)="onDependencyChange($event.value)"
      >
        <mat-option [value]="null">Seleccione Dependencia</mat-option>
        @for (dependency of dependencies; track dependency) {
          <mat-option [value]="dependency.id">
            {{ dependency.name }}
          </mat-option>
        }
      </mat-select>
      @if (contractForm.get("dependency")?.invalid) {
        <mat-error>
          <mat-icon>error_outline</mat-icon>
          Este campo es obligatorio
        </mat-error>
      }
    </mat-form-field>
    <mat-form-field class="example-half-width" appearance="outline">
      <mat-label>Grupo</mat-label>
      <mat-icon matPrefix>group</mat-icon>
      <mat-select formControlName="group">
        <mat-option [value]="null">Seleccione grupo</mat-option>
        @for (group of filteredGroups; track group) {
          <mat-option [value]="group.id">
            {{ group.name }}
          </mat-option>
        }
      </mat-select>
      @if (contractForm.get("group")?.invalid) {
        <mat-error>
          <mat-icon>error_outline</mat-icon>
          Este campo es obligatorio
        </mat-error>
      }
    </mat-form-field>
  </div>
  <div class="row">
    <mat-form-field class="example-half-width" appearance="outline">
      <mat-label>Seleccione Departamento Ejecucion Del Contrato*</mat-label>
      <mat-icon matPrefix>location_on</mat-icon>
      <input
        type="text"
        matInput
        [formControl]="departmentSearchCtrl"
        [matAutocomplete]="departmentAuto"
        [errorStateMatcher]="departmentErrorMatcher"
        placeholder="Buscar departamento"
        (focus)="showAllDepartments()"
        (click)="showAllDepartments()"
        aria-label="Buscar departamento"
        #departmentInput
      />
      <mat-autocomplete
        #departmentAuto="matAutocomplete"
        [displayWith]="displayDepartment"
        (optionSelected)="handleDepartmentSelection($event)"
        [panelWidth]="'auto'"
      >
        @for (department of filteredDepartments | async; track department) {
          <mat-option [value]="department.name">
            {{ department.name }}
          </mat-option>
        }
      </mat-autocomplete>
      <mat-error>
        <mat-icon>error_outline</mat-icon>
        Este campo es obligatorio
      </mat-error>
    </mat-form-field>
    <mat-form-field class="example-half-width" appearance="outline">
      <mat-label>Seleccione Municipio Ejecucion Del Contrato*</mat-label>
      <mat-icon matPrefix>location_on</mat-icon>
      <input
        type="text"
        matInput
        [formControl]="municipalitySearchCtrl"
        [matAutocomplete]="municipalityAuto"
        [errorStateMatcher]="municipalityErrorMatcher"
        placeholder="Buscar municipio"
        (focus)="showAllMunicipalities()"
        (click)="showAllMunicipalities()"
        aria-label="Buscar municipio"
        #municipalityInput
      />
      <mat-autocomplete
        #municipalityAuto="matAutocomplete"
        [displayWith]="displayMunicipality"
        (optionSelected)="handleMunicipalitySelection($event)"
        [panelWidth]="'auto'"
      >
        @for (municipality of filteredMunicipalities; track municipality) {
          <mat-option [value]="municipality.name">
            {{ municipality.name }}
          </mat-option>
        }
      </mat-autocomplete>
      <mat-error>
        <mat-icon>error_outline</mat-icon>
        Este campo es obligatorio
      </mat-error>
    </mat-form-field>
  </div>
  <div class="row">
    <mat-form-field class="example-full-width" appearance="outline">
      <mat-label>Supervisor</mat-label>
      <mat-icon matPrefix>person</mat-icon>
      <input
        type="text"
        matInput
        formControlName="supervisorFullName"
        [matAutocomplete]="autoSupervisor"
        placeholder="Buscar supervisor"
        required
      />
      @if (supervisorFullNameControl.value) {
        <button mat-icon-button matSuffix (click)="clearSupervisor()">
          <mat-icon>close</mat-icon>
        </button>
      }
      <mat-autocomplete
        #autoSupervisor="matAutocomplete"
        [displayWith]="displaySupervisorName"
        (optionSelected)="onSupervisorSelected($event)"
      >
        @for (supervisor of filteredSupervisors | async; track supervisor) {
          <mat-option [value]="supervisor">
            {{ supervisor.fullName }}
          </mat-option>
        }
      </mat-autocomplete>
      @if (supervisorFullNameControl.hasError("required")) {
        <mat-error>
          <mat-icon>error_outline</mat-icon>
          Este campo es obligatorio
        </mat-error>
      }
    </mat-form-field>
  </div>
  <div class="row">
    <mat-form-field class="example-full-width" appearance="outline">
      <mat-label>Pago Mensual</mat-label>
      <mat-icon matPrefix>payments</mat-icon>
      <input
        matInput
        formControlName="monthlyPayment"
        currencyMask
        required
        title="Pago Mensual"
        placeholder="Ingrese valor del pago mensual"
      />
      @if (contractForm.get("monthlyPayment")?.hasError("required")) {
        <mat-error>
          <mat-icon>error_outline</mat-icon>
          Este campo es obligatorio
        </mat-error>
      }
      @if (
        contractForm.get("monthlyPayment")?.hasError("min") ||
        contractForm.get("monthlyPayment")?.value === 0
      ) {
        <mat-error>
          <mat-icon>error_outline</mat-icon>
          El valor debe ser mayor que $0
        </mat-error>
      }
    </mat-form-field>
  </div>
  <div class="row">
    <div class="toggle-container">
      <mat-slide-toggle formControlName="warranty">
        Tiene Garantía
      </mat-slide-toggle>
    </div>
  </div>

  @if (contractForm.get("warranty")?.value) {
    <div class="row">
      <mat-form-field class="example-half-width" appearance="outline">
        <mat-label>Fecha de Garantia</mat-label>
        <mat-icon matPrefix>date_range</mat-icon>
        <input
          matInput
          formControlName="dateExpeditionWarranty"
          [matDatepicker]="picker"
          title="Fecha de Garantia"
        />
        <mat-hint>DD/MM/YYYY</mat-hint>
        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
        @if (contractForm.get("dateExpeditionWarranty")?.hasError("required")) {
          <mat-error>
            <mat-icon>error_outline</mat-icon>
            La fecha de garantía es requerida
          </mat-error>
        }
      </mat-form-field>

      <mat-form-field class="example-half-width" appearance="outline">
        <mat-label>Seleccione Tipo de poliza</mat-label>
        <mat-icon matPrefix>policy</mat-icon>
        <mat-select
          formControlName="typeWarrantyId"
          title="Seleccione Tipo de poliza"
        >
          <mat-option [value]="null">Seleccione Tipo de poliza</mat-option>
          @for (type of typeWarranty; track type) {
            <mat-option [value]="type.id">
              {{ type.name }}
            </mat-option>
          }
        </mat-select>
        @if (contractForm.get("typeWarrantyId")?.hasError("required")) {
          <mat-error>
            <mat-icon>error_outline</mat-icon>
            Este campo es obligatorio
          </mat-error>
        }
      </mat-form-field>
    </div>

    <div class="row">
      <mat-form-field class="example-full-width" appearance="outline">
        <mat-label>Seleccione tipo garantia</mat-label>
        <mat-icon matPrefix>security</mat-icon>
        <mat-select
          formControlName="insuredRisksId"
          title="Seleccione tipo garantia"
        >
          <mat-option [value]="null">Seleccione tipo garantia</mat-option>
          @for (type of insuredRisks; track type) {
            <mat-option [value]="type.id">
              {{ type.name }}
            </mat-option>
          }
        </mat-select>
        @if (contractForm.get("insuredRisksId")?.hasError("required")) {
          <mat-error>
            <mat-icon>error_outline</mat-icon>
            Este campo es obligatorio
          </mat-error>
        }
      </mat-form-field>
    </div>
  }
  <div class="row">
    <div class="toggle-container">
      <mat-slide-toggle formControlName="addition"> Adición </mat-slide-toggle>
      <mat-slide-toggle formControlName="cession"> Cesión </mat-slide-toggle>
      <mat-slide-toggle
        formControlName="earlyTermination"
        (change)="earlyTerminationToggled.emit()"
      >
        Terminación Anticipada
      </mat-slide-toggle>
      <mat-slide-toggle formControlName="settled"> Liquidado </mat-slide-toggle>
      <mat-slide-toggle formControlName="rup" (change)="onRupChange($event)">
        RUP
      </mat-slide-toggle>
      <mat-slide-toggle
        formControlName="completed"
        (change)="onCompletedChange($event)"
      >
        Contrato Finalizado
      </mat-slide-toggle>
    </div>
  </div>
</form>
