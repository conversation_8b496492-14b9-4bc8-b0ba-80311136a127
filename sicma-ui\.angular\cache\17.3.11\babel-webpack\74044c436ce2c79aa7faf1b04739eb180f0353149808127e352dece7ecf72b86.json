{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { HttpClient } from '@angular/common/http';\nimport { Injectable } from '@angular/core';\nimport { environment } from '@env';\nlet EmailTemplateService = class EmailTemplateService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.apiUrl}/email-templates`;\n  }\n  getTemplateByName(templateName) {\n    return this.http.get(`${this.apiUrl}/${templateName}`);\n  }\n  sendEmail(templateName, request) {\n    return this.http.post(`${this.apiUrl}/send/${templateName}`, request);\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: HttpClient\n    }];\n  }\n};\nEmailTemplateService = __decorate([Injectable({\n  providedIn: 'root'\n})], EmailTemplateService);\nexport { EmailTemplateService };", "map": {"version": 3, "names": ["HttpClient", "Injectable", "environment", "EmailTemplateService", "constructor", "http", "apiUrl", "getTemplateByName", "templateName", "get", "sendEmail", "request", "post", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\shared\\services\\email-template.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\nimport { Injectable } from '@angular/core';\nimport { environment } from '@env';\nimport { Observable } from 'rxjs';\n\nimport {\n  EmailTemplate,\n  SendEmailRequest,\n} from '@shared/models/email-template.model';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class EmailTemplateService {\n  private readonly apiUrl = `${environment.apiUrl}/email-templates`;\n\n  constructor(private readonly http: HttpClient) {}\n\n  getTemplateByName(templateName: string): Observable<EmailTemplate> {\n    return this.http.get<EmailTemplate>(`${this.apiUrl}/${templateName}`);\n  }\n\n  sendEmail(\n    templateName: string,\n    request: SendEmailRequest,\n  ): Observable<{ message: string }> {\n    return this.http.post<{ message: string }>(\n      `${this.apiUrl}/send/${templateName}`,\n      request,\n    );\n  }\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,sBAAsB;AACjD,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,WAAW,QAAQ,MAAM;AAW3B,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAG/BC,YAA6BC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACI,MAAM,kBAAkB;EAEjB;EAEhDC,iBAAiBA,CAACC,YAAoB;IACpC,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAgB,GAAG,IAAI,CAACH,MAAM,IAAIE,YAAY,EAAE,CAAC;EACvE;EAEAE,SAASA,CACPF,YAAoB,EACpBG,OAAyB;IAEzB,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CACnB,GAAG,IAAI,CAACN,MAAM,SAASE,YAAY,EAAE,EACrCG,OAAO,CACR;EACH;;;;;;;AAjBWR,oBAAoB,GAAAU,UAAA,EAHhCZ,UAAU,CAAC;EACVa,UAAU,EAAE;CACb,CAAC,C,EACWX,oBAAoB,CAkBhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}