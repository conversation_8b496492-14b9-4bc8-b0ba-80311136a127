{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./contract-detail-form.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./contract-detail-form.component.scss?ngResource\";\nimport { AlertService } from '@shared/services/alert.service';\nimport { forkJoin, of } from 'rxjs';\nimport { debounceTime, finalize, map, startWith, switchMap } from 'rxjs/operators';\nimport { ContractService } from '../../services/contract.service';\nimport { AsyncPipe } from '@angular/common';\nimport { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';\nimport { FormBuilder, FormControl, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatAutocompleteModule, MatAutocompleteTrigger } from '@angular/material/autocomplete';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { CausesSelectionService } from '@contract-management/services/causes-selection.service';\nimport { ContractClassService } from '@contract-management/services/contract-class.service';\nimport { DependencyService } from '@contract-management/services/dependency.service';\nimport { GroupService } from '@contract-management/services/group.service';\nimport { InsuredRisksService } from '@contract-management/services/insured_risks.service';\nimport { ManagementSupportService } from '@contract-management/services/management-support.service';\nimport { TypeWarrantyService } from '@contract-management/services/type_warranty.service';\nimport { DepartmentService } from '@shared/services/department.service';\nimport { MunicipalityService } from '@shared/services/municipality.service';\nimport { SupervisorService } from '@supervisor-management/services/supervisor.service';\nimport { NgxCurrencyDirective } from 'ngx-currency';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { ContractTypeService } from '../../services/contract-type.service';\nimport { ContractYearService } from '../../services/contract-year.service';\nimport { SelectionModalityService } from '../../services/selection-modality.service';\nimport { StatusService } from '../../services/status.service';\nimport { TrackingTypeService } from '../../services/tracking-type.service';\nlet ContractDetailFormComponent = class ContractDetailFormComponent {\n  constructor(selectionModalityService, trackingTypeService, contractTypeService, alert, formBuilder, contractYearService, contractService, supervisorService, typeWarrantyService, insuredRisksService, dependencyService, groupservice, departmentService, municipalityService, managementSupportService, causesSelectionService, contractClassService, statusService, spinner) {\n    this.selectionModalityService = selectionModalityService;\n    this.trackingTypeService = trackingTypeService;\n    this.contractTypeService = contractTypeService;\n    this.alert = alert;\n    this.formBuilder = formBuilder;\n    this.contractYearService = contractYearService;\n    this.contractService = contractService;\n    this.supervisorService = supervisorService;\n    this.typeWarrantyService = typeWarrantyService;\n    this.insuredRisksService = insuredRisksService;\n    this.dependencyService = dependencyService;\n    this.groupservice = groupservice;\n    this.departmentService = departmentService;\n    this.municipalityService = municipalityService;\n    this.managementSupportService = managementSupportService;\n    this.causesSelectionService = causesSelectionService;\n    this.contractClassService = contractClassService;\n    this.statusService = statusService;\n    this.spinner = spinner;\n    this.contract = null;\n    this.contractForm = this.formBuilder.group({\n      contractNumber: ['', [Validators.required, Validators.min(1)]],\n      contractYear: ['', Validators.required],\n      object: ['', Validators.required],\n      sigepLink: [''],\n      secopLink: [''],\n      rup: [false],\n      addition: [{\n        value: false,\n        disabled: true\n      }],\n      cession: [{\n        value: false,\n        disabled: true\n      }],\n      settled: [{\n        value: false,\n        disabled: true\n      }],\n      selectionModality: ['', Validators.required],\n      trackingType: ['', Validators.required],\n      contractType: ['', Validators.required],\n      dependency: ['', Validators.required],\n      group: ['', Validators.required],\n      earlyTermination: [{\n        value: null,\n        disabled: true\n      }],\n      monthlyPayment: [null, [Validators.required, Validators.min(1)]],\n      municipalityId: ['', Validators.required],\n      departmentId: ['', Validators.required],\n      secopCode: ['', Validators.required],\n      warranty: [false],\n      dateExpeditionWarranty: [''],\n      typeWarrantyId: [''],\n      insuredRisksId: [''],\n      supervisorId: [null, Validators.required],\n      supervisorFullName: ['', Validators.required],\n      causesSelectionId: ['', Validators.required],\n      contractClassId: ['', Validators.required],\n      managementSupportId: ['', Validators.required],\n      completed: [false]\n    });\n    this.earlyTerminationToggled = new EventEmitter();\n    this.contractCompletedChanged = new EventEmitter();\n    this.selectionModalities = [];\n    this.trackingTypes = [];\n    this.contractTypes = [];\n    this.contractYears = [];\n    this.typeWarranty = [];\n    this.insuredRisks = [];\n    this.dependencies = [];\n    this.groups = [];\n    this.filteredGroups = [];\n    this.departments = [];\n    this.municipalities = [];\n    this.filteredMunicipalities = [];\n    this.supervisors = [];\n    this.managementSupport = [];\n    this.causesSelection = [];\n    this.contractClass = [];\n    this.supervisor = null;\n    this.supervisorFullNameControl = this.contractForm.get('supervisorFullName');\n    this.filteredSupervisors = this.supervisorFullNameControl.valueChanges.pipe(startWith(''), map(value => {\n      const searchValue = typeof value === 'string' ? value.toLowerCase() : '';\n      return this.supervisors.filter(supervisor => supervisor.fullName.toLowerCase().includes(searchValue));\n    }));\n    this.departmentSearchCtrl = new FormControl('');\n    this.municipalitySearchCtrl = new FormControl('');\n    this.filteredDepartments = this.departmentSearchCtrl.valueChanges.pipe(startWith(''), map(value => this._filterDepartments(value || '')));\n    this.filteredMunicipalities = [];\n    this.departmentSearchCtrl.valueChanges.subscribe(value => {\n      if (value) {\n        this.municipalitySearchCtrl.enable();\n      } else {\n        this.municipalitySearchCtrl.disable();\n      }\n    });\n  }\n  ngOnInit() {\n    this.loadData();\n    this.setupContractNumberValidation();\n    this.setupWarrantyValidation();\n    if (!this.departmentSearchCtrl.value) {\n      this.municipalitySearchCtrl.disable();\n    }\n    if (this.contract) {\n      this.contractForm.patchValue({\n        ...this.contract,\n        contractYear: this.contract.contractYear?.id,\n        selectionModality: this.contract.selectionModality?.id,\n        trackingType: this.contract.trackingType?.id,\n        contractType: this.contract.contractType?.id,\n        dependency: this.contract.dependency?.id,\n        group: this.contract.group?.id,\n        municipalityId: this.contract.municipality?.id,\n        departmentId: this.contract.department?.id,\n        causesSelectionId: this.contract.causesSelection?.id,\n        contractClassId: this.contract.contractClass?.id,\n        managementSupportId: this.contract.managementSupport?.id\n      });\n      // Set supervisor data if available\n      if (this.contract.supervisor) {\n        this.supervisor = this.contract.supervisor;\n        this.contractForm.patchValue({\n          supervisorId: this.contract.supervisor.id,\n          supervisorFullName: this.contract.supervisor.fullName\n        });\n      }\n      const department = this.contract.department;\n      if (department) {\n        this.departmentSearchCtrl.setValue(department.name);\n        if (this.contract.municipality) {\n          this.municipalitySearchCtrl.setValue(this.contract.municipality.name);\n        }\n      }\n      if (!this.contract.earlyTermination) {\n        this.contractForm.get('earlyTermination')?.enable();\n      }\n      if (this.contract.status) {\n        const isFinished = this.contract.status.name === 'FINALIZADO';\n        this.contractForm.get('completed')?.setValue(isFinished);\n        if (isFinished) {\n          this.setFormControlsState(false);\n        }\n      }\n    }\n  }\n  loadData() {\n    forkJoin({\n      selectionModalities: this.selectionModalityService.getAll(),\n      trackingTypes: this.trackingTypeService.getAll(),\n      contractTypes: this.contractTypeService.getAll(),\n      contractYears: this.contractYearService.getAll(),\n      typeWarranty: this.typeWarrantyService.getAll(),\n      insuredRisks: this.insuredRisksService.getAll(),\n      dependencies: this.dependencyService.getAll(),\n      groups: this.groupservice.getAll(),\n      department: this.departmentService.getAll(),\n      municipality: this.municipalityService.getAll(),\n      supervisors: this.supervisorService.getAll(),\n      managementSupport: this.managementSupportService.getAll(),\n      causesSelection: this.causesSelectionService.getAll(),\n      contractClass: this.contractClassService.getAll()\n    }).subscribe({\n      next: ({\n        selectionModalities,\n        trackingTypes,\n        contractTypes,\n        contractYears,\n        typeWarranty,\n        insuredRisks,\n        dependencies,\n        groups,\n        department,\n        municipality,\n        supervisors,\n        managementSupport,\n        causesSelection,\n        contractClass\n      }) => {\n        this.selectionModalities = selectionModalities;\n        this.trackingTypes = trackingTypes;\n        this.contractTypes = contractTypes;\n        this.contractYears = contractYears;\n        this.typeWarranty = typeWarranty;\n        this.insuredRisks = insuredRisks;\n        this.dependencies = dependencies;\n        this.groups = groups;\n        this.departments = department;\n        this.municipalities = municipality;\n        this.supervisors = supervisors;\n        this.managementSupport = managementSupport;\n        this.causesSelection = causesSelection;\n        this.contractClass = contractClass;\n        if (!this.contract) {\n          const currentYear = new Date().getFullYear();\n          const defaultYear = this.contractYears.find(y => y.year === currentYear);\n          if (defaultYear) {\n            this.contractForm.get('contractYear')?.setValue(defaultYear.id);\n          }\n        }\n        if (this.contract?.dependency?.id) {\n          this.onDependencyChange(this.contract.dependency.id);\n        }\n        if (this.contract?.department?.id) {\n          this.onDepartmentChange(this.contract.department.id);\n        }\n        this.filteredDepartments = this.departmentSearchCtrl.valueChanges.pipe(startWith(''), map(value => this._filterDepartments(value || '')));\n      },\n      error: error => {\n        this.alert.error(error.error?.detail ?? 'Error al cargar los datos');\n      }\n    });\n  }\n  onDependencyChange(dependencyId) {\n    if (dependencyId != null) {\n      this.filteredGroups = this.groups.filter(group => group.dependency.id === dependencyId);\n    } else {\n      this.filteredGroups = [];\n    }\n  }\n  onDepartmentChange(departmentId) {\n    if (departmentId != null) {\n      this.municipalitySearchCtrl.enable();\n      this.loadMunicipalities(departmentId);\n    } else {\n      this.filteredMunicipalities = [];\n      this.municipalitySearchCtrl.setValue('');\n      this.municipalitySearchCtrl.disable();\n      this.contractForm.get('municipalityId')?.setValue(null);\n    }\n  }\n  loadMunicipalities(departmentId) {\n    this.municipalityService.getAllByDepartmentId(departmentId).subscribe({\n      next: municipalities => {\n        this.municipalities = municipalities;\n        this.filteredMunicipalities = this.municipalities;\n        this.municipalitySearchCtrl.valueChanges.pipe(startWith(''), map(value => this._filterMunicipalities(value || ''))).subscribe(filtered => {\n          this.filteredMunicipalities = filtered;\n        });\n      },\n      error: error => {\n        this.alert.error(error.error?.detail ?? 'Error al cargar municipios');\n      }\n    });\n  }\n  handleDepartmentSelection(event) {\n    const selectedDepartmentName = event.option.viewValue;\n    const selectedDepartment = this.departments.find(dept => dept.name === selectedDepartmentName);\n    if (selectedDepartment) {\n      this.contractForm.get('departmentId')?.setValue(selectedDepartment.id);\n      this.contractForm.get('departmentId')?.markAsTouched();\n      this.contractForm.get('municipalityId')?.setValue(null);\n      this.contractForm.get('municipalityId')?.markAsTouched();\n      this.municipalitySearchCtrl.setValue('');\n      this.onDepartmentChange(selectedDepartment.id);\n      setTimeout(() => {\n        this.departmentAutocompleteTrigger?.closePanel();\n      }, 0);\n    }\n  }\n  handleMunicipalitySelection(event) {\n    const selectedMunicipalityName = event.option.viewValue;\n    const selectedMunicipality = this.municipalities.find(mun => mun.name === selectedMunicipalityName);\n    if (selectedMunicipality) {\n      this.contractForm.get('municipalityId')?.setValue(selectedMunicipality.id);\n      this.contractForm.get('municipalityId')?.markAsTouched();\n      setTimeout(() => {\n        this.municipalityAutocompleteTrigger?.closePanel();\n      }, 0);\n    }\n  }\n  _filterDepartments(value) {\n    const filterValue = value.toLowerCase();\n    return this.departments.filter(department => department.name.toLowerCase().includes(filterValue));\n  }\n  _filterMunicipalities(value) {\n    const filterValue = value.toLowerCase();\n    return this.municipalities.filter(municipality => municipality.name.toLowerCase().includes(filterValue));\n  }\n  displayDepartment(departmentName) {\n    return departmentName;\n  }\n  displayMunicipality(municipalityName) {\n    return municipalityName;\n  }\n  displaySupervisorName(supervisor) {\n    if (typeof supervisor === 'string') {\n      return supervisor;\n    }\n    return supervisor ? supervisor.fullName : '';\n  }\n  showAllDepartments() {\n    if (!this.departmentAutocompleteTrigger?.panelOpen) {\n      this.departmentSearchCtrl.setValue('');\n      this.contractForm.get('departmentId')?.markAsTouched();\n      setTimeout(() => {\n        this.departmentAutocompleteTrigger?.openPanel();\n      }, 0);\n    }\n  }\n  showAllMunicipalities() {\n    if (this.departmentSearchCtrl.value && !this.municipalityAutocompleteTrigger?.panelOpen) {\n      this.municipalitySearchCtrl.setValue('');\n      this.contractForm.get('municipalityId')?.markAsTouched();\n      setTimeout(() => {\n        this.municipalityAutocompleteTrigger?.openPanel();\n      }, 0);\n    }\n  }\n  onRupChange(event) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.contract?.id) return;\n      if (event.checked) {\n        const confirmed = yield _this.alert.confirm('¿Está seguro de marcar RUP?');\n        if (confirmed) {\n          _this.spinner.show();\n          _this.contractService.update(_this.contract.id, {\n            rup: true\n          }).pipe(finalize(() => _this.spinner.hide())).subscribe({\n            next: () => {\n              _this.contractForm.get('rup')?.setValue(true);\n              _this.alert.success('El contrato ha sido marcado como RUP.');\n            },\n            error: error => {\n              _this.contractForm.get('rup')?.setValue(false);\n              _this.alert.error(error.error?.detail ?? 'Error al actualizar el contrato');\n            }\n          });\n        } else {\n          _this.contractForm.get('rup')?.setValue(false);\n        }\n      } else {\n        const confirmed = yield _this.alert.confirm('¿Está seguro de desmarcar RUP?');\n        if (confirmed) {\n          _this.spinner.show();\n          _this.contractService.update(_this.contract.id, {\n            rup: false\n          }).pipe(finalize(() => _this.spinner.hide())).subscribe({\n            next: () => {\n              _this.contractForm.get('rup')?.setValue(false);\n              _this.alert.success('El contrato ha sido desmarcado como RUP.');\n            },\n            error: error => {\n              _this.contractForm.get('rup')?.setValue(true);\n              _this.alert.error(error.error?.detail ?? 'Error al actualizar el contrato');\n            }\n          });\n        } else {\n          _this.contractForm.get('rup')?.setValue(true);\n        }\n      }\n    })();\n  }\n  onCompletedChange(event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.contract?.id) return;\n      if (event.checked) {\n        const confirmed = yield _this2.alert.confirm('¿Está seguro de marcar el contrato como finalizado?');\n        if (confirmed) {\n          _this2.spinner.show();\n          _this2.statusService.getByName('FINALIZADO').pipe(switchMap(status => _this2.contractService.update(_this2.contract.id, {\n            statusId: status.id\n          })), finalize(() => _this2.spinner.hide())).subscribe({\n            next: () => {\n              _this2.contractForm.get('completed')?.setValue(true);\n              _this2.setFormControlsState(false);\n              _this2.alert.success('El contrato ha sido marcado como finalizado.');\n              _this2.contractCompletedChanged.emit(true);\n            },\n            error: error => {\n              _this2.contractForm.get('completed')?.setValue(false);\n              _this2.alert.error(error.error?.detail ?? 'Error al actualizar el estado del contrato');\n            }\n          });\n        } else {\n          _this2.contractForm.get('completed')?.setValue(false);\n        }\n      } else {\n        const confirmed = yield _this2.alert.confirm('¿Está seguro de desmarcar el contrato como finalizado?');\n        if (confirmed) {\n          _this2.spinner.show();\n          _this2.statusService.getByName('EN EJECUCIÓN').pipe(switchMap(status => _this2.contractService.update(_this2.contract.id, {\n            statusId: status.id\n          })), finalize(() => _this2.spinner.hide())).subscribe({\n            next: () => {\n              _this2.contractForm.get('completed')?.setValue(false);\n              _this2.setFormControlsState(true);\n              _this2.alert.success('El contrato ha sido marcado como en ejecución.');\n              _this2.contractCompletedChanged.emit(false);\n            },\n            error: error => {\n              _this2.contractForm.get('completed')?.setValue(true);\n              _this2.alert.error(error.error?.detail ?? 'Error al actualizar el estado del contrato');\n            }\n          });\n        } else {\n          _this2.contractForm.get('completed')?.setValue(true);\n        }\n      }\n    })();\n  }\n  setFormControlsState(enable) {\n    const excludedControls = ['completed'];\n    Object.keys(this.contractForm.controls).forEach(controlName => {\n      if (!excludedControls.includes(controlName)) {\n        const control = this.contractForm.get(controlName);\n        if (enable) {\n          control?.enable();\n        } else {\n          control?.disable();\n        }\n      }\n    });\n  }\n  setupContractNumberValidation() {\n    this.contractForm.get('contractNumber')?.valueChanges.pipe(debounceTime(300), switchMap(contractNumber => {\n      const contractYearId = this.contractForm.get('contractYear')?.value;\n      if (contractNumber && contractYearId) {\n        return this.contractService.validateContractNumber(contractNumber, contractYearId);\n      }\n      return of(true);\n    })).subscribe(isValid => {\n      if (!isValid) {\n        this.contractForm.get('contractNumber')?.setErrors({\n          duplicateContract: true\n        });\n      }\n    });\n    this.contractForm.get('contractYear')?.valueChanges.subscribe(() => {\n      this.contractForm.get('contractNumber')?.updateValueAndValidity();\n    });\n  }\n  onSupervisorSelected(event) {\n    const supervisor = event.option.value;\n    this.supervisor = supervisor;\n    this.contractForm.patchValue({\n      supervisorId: supervisor.id,\n      supervisorFullName: supervisor.fullName\n    });\n  }\n  clearSupervisor() {\n    this.supervisor = null;\n    this.contractForm.patchValue({\n      supervisorId: null,\n      supervisorFullName: ''\n    });\n  }\n  setupWarrantyValidation() {\n    const dateExpeditionWarranty = this.contractForm.get('dateExpeditionWarranty');\n    const typeWarrantyId = this.contractForm.get('typeWarrantyId');\n    const insuredRisksId = this.contractForm.get('insuredRisksId');\n    const updateValidation = hasWarranty => {\n      if (hasWarranty) {\n        dateExpeditionWarranty?.setValidators([Validators.required]);\n        typeWarrantyId?.setValidators([Validators.required]);\n        insuredRisksId?.setValidators([Validators.required]);\n      } else {\n        dateExpeditionWarranty?.setValidators(null);\n        typeWarrantyId?.setValidators(null);\n        insuredRisksId?.setValidators(null);\n        dateExpeditionWarranty?.patchValue(null, {\n          emitEvent: false\n        });\n        typeWarrantyId?.patchValue(null, {\n          emitEvent: false\n        });\n        insuredRisksId?.patchValue(null, {\n          emitEvent: false\n        });\n      }\n      dateExpeditionWarranty?.markAsUntouched();\n      typeWarrantyId?.markAsUntouched();\n      insuredRisksId?.markAsUntouched();\n      dateExpeditionWarranty?.updateValueAndValidity({\n        emitEvent: false\n      });\n      typeWarrantyId?.updateValueAndValidity({\n        emitEvent: false\n      });\n      insuredRisksId?.updateValueAndValidity({\n        emitEvent: false\n      });\n    };\n    const initialWarranty = this.contractForm.get('warranty')?.value || false;\n    updateValidation(initialWarranty);\n    this.contractForm.get('warranty')?.valueChanges.subscribe(updateValidation);\n  }\n  isValid() {\n    return this.contractForm.valid;\n  }\n  getValue() {\n    Object.keys(this.contractForm.controls).forEach(key => {\n      const control = this.contractForm.get(key);\n      if (control) {\n        control.markAsTouched();\n      }\n    });\n    return this.contractForm.getRawValue();\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: SelectionModalityService\n    }, {\n      type: TrackingTypeService\n    }, {\n      type: ContractTypeService\n    }, {\n      type: AlertService\n    }, {\n      type: FormBuilder\n    }, {\n      type: ContractYearService\n    }, {\n      type: ContractService\n    }, {\n      type: SupervisorService\n    }, {\n      type: TypeWarrantyService\n    }, {\n      type: InsuredRisksService\n    }, {\n      type: DependencyService\n    }, {\n      type: GroupService\n    }, {\n      type: DepartmentService\n    }, {\n      type: MunicipalityService\n    }, {\n      type: ManagementSupportService\n    }, {\n      type: CausesSelectionService\n    }, {\n      type: ContractClassService\n    }, {\n      type: StatusService\n    }, {\n      type: NgxSpinnerService\n    }];\n  }\n  static {\n    this.propDecorators = {\n      departmentAutocomplete: [{\n        type: ViewChild,\n        args: ['departmentAuto']\n      }],\n      departmentAutocompleteTrigger: [{\n        type: ViewChild,\n        args: ['departmentInput', {\n          read: MatAutocompleteTrigger\n        }]\n      }],\n      municipalityAutocomplete: [{\n        type: ViewChild,\n        args: ['municipalityAuto']\n      }],\n      municipalityAutocompleteTrigger: [{\n        type: ViewChild,\n        args: ['municipalityInput', {\n          read: MatAutocompleteTrigger\n        }]\n      }],\n      contract: [{\n        type: Input\n      }],\n      earlyTerminationToggled: [{\n        type: Output\n      }],\n      contractCompletedChanged: [{\n        type: Output\n      }]\n    };\n  }\n};\nContractDetailFormComponent = __decorate([Component({\n  selector: 'app-contract-detail-form',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  imports: [ReactiveFormsModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatSlideToggleModule, MatAutocompleteModule, MatIconModule, MatButtonModule, MatDatepickerModule, AsyncPipe, NgxCurrencyDirective],\n  styles: [__NG_CLI_RESOURCE__1]\n})], ContractDetailFormComponent);\nexport { ContractDetailFormComponent };", "map": {"version": 3, "names": ["AlertService", "fork<PERSON><PERSON>n", "of", "debounceTime", "finalize", "map", "startWith", "switchMap", "ContractService", "AsyncPipe", "Component", "EventEmitter", "Input", "Output", "ViewChild", "FormBuilder", "FormControl", "ReactiveFormsModule", "Validators", "MatSlideToggleModule", "MatAutocompleteModule", "MatAutocompleteTrigger", "MatDatepickerModule", "MatButtonModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatSelectModule", "CausesSelectionService", "ContractClassService", "DependencyService", "GroupService", "InsuredRisksService", "ManagementSupportService", "TypeWarrantyService", "DepartmentService", "MunicipalityService", "SupervisorService", "NgxCurrencyDirective", "NgxSpinnerService", "ContractTypeService", "ContractYearService", "SelectionModalityService", "StatusService", "TrackingTypeService", "ContractDetailFormComponent", "constructor", "selectionModalityService", "trackingTypeService", "contractTypeService", "alert", "formBuilder", "contractYearService", "contractService", "supervisorService", "typeWarrantyService", "insuredRisksService", "dependencyService", "groupservice", "departmentService", "municipalityService", "managementSupportService", "causesSelectionService", "contractClassService", "statusService", "spinner", "contract", "contractForm", "group", "contractNumber", "required", "min", "contractYear", "object", "sigepLink", "secopLink", "rup", "addition", "value", "disabled", "cession", "settled", "selectionModality", "trackingType", "contractType", "dependency", "earlyTermination", "monthlyPayment", "municipalityId", "departmentId", "secopCode", "warranty", "dateExpeditionWarranty", "typeWarrantyId", "insuredRisksId", "supervisorId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "causesSelectionId", "contractClassId", "managementSupportId", "completed", "earlyTerminationToggled", "contractCompletedChanged", "selectionModalities", "trackingTypes", "contractTypes", "contractYears", "typeWarranty", "insuredRisks", "dependencies", "groups", "filteredGroups", "departments", "municipalities", "filteredMunicipalities", "supervisors", "managementSupport", "causesSelection", "contractClass", "supervisor", "supervisorFullNameControl", "get", "filteredSupervisors", "valueChanges", "pipe", "searchValue", "toLowerCase", "filter", "fullName", "includes", "departmentSearchCtrl", "municipalitySearchCtrl", "filteredDepartments", "_filterDepartments", "subscribe", "enable", "disable", "ngOnInit", "loadData", "setupContractNumberValidation", "setupWarrantyValidation", "patchValue", "id", "municipality", "department", "setValue", "name", "status", "isFinished", "setFormControlsState", "getAll", "next", "currentYear", "Date", "getFullYear", "defaultYear", "find", "y", "year", "onDependencyChange", "onDepartmentChange", "error", "detail", "dependencyId", "loadMunicipalities", "getAllByDepartmentId", "_filterMunicipalities", "filtered", "handleDepartmentSelection", "event", "selectedDepartmentName", "option", "viewValue", "selectedDepartment", "dept", "<PERSON><PERSON><PERSON><PERSON>ched", "setTimeout", "departmentAutocompleteTrigger", "closePanel", "handleMunicipalitySelection", "selectedMunicipalityName", "selectedMunicipality", "mun", "municipalityAutocompleteTrigger", "filterValue", "displayDepartment", "departmentName", "displayMunicipality", "municipalityName", "displaySupervisorName", "showAllDepartments", "panelOpen", "openPanel", "showAllMunicipalities", "onRupChange", "_this", "_asyncToGenerator", "checked", "confirmed", "confirm", "show", "update", "hide", "success", "onCompletedChange", "_this2", "getByName", "statusId", "emit", "excludedControls", "Object", "keys", "controls", "for<PERSON>ach", "controlName", "control", "contractYearId", "validateContractNumber", "<PERSON><PERSON><PERSON><PERSON>", "setErrors", "duplicateContract", "updateValueAndValidity", "onSupervisorSelected", "clearSupervisor", "updateValidation", "hasW<PERSON>nty", "setValidators", "emitEvent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initialWarranty", "valid", "getValue", "key", "getRawValue", "args", "read", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\contract-detail-form\\contract-detail-form.component.ts"], "sourcesContent": ["import { AlertService } from '@shared/services/alert.service';\nimport { forkJoin, Observable, of } from 'rxjs';\nimport {\n  debounceTime,\n  finalize,\n  map,\n  startWith,\n  switchMap,\n} from 'rxjs/operators';\nimport { ContractService } from '../../services/contract.service';\n\nimport { AsyncPipe } from '@angular/common';\nimport {\n  Component,\n  EventEmitter,\n  Input,\n  OnInit,\n  Output,\n  ViewChild,\n} from '@angular/core';\nimport {\n  FormBuilder,\n  FormControl,\n  FormGroup,\n  ReactiveFormsModule,\n  Validators,\n} from '@angular/forms';\nimport {\n  MatSlideToggleChange,\n  MatSlideToggleModule,\n} from '@angular/material/slide-toggle';\n\nimport {\n  MatAutocomplete,\n  MatAutocompleteModule,\n  MatAutocompleteSelectedEvent,\n  MatAutocompleteTrigger,\n} from '@angular/material/autocomplete';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\n\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { CausesSelection } from '@contract-management/models/causes-seletion.model';\nimport { ContractClass } from '@contract-management/models/contract-class.model';\nimport { Contract } from '@contract-management/models/contract.model';\nimport { Dependency } from '@contract-management/models/dependency.model';\nimport { Group } from '@contract-management/models/group.model';\nimport { InsuredRisks } from '@contract-management/models/insured_risks.model';\nimport { ManagementSupport } from '@contract-management/models/management-support.model';\nimport { TypeWarranty } from '@contract-management/models/type_warranty.model';\nimport { CausesSelectionService } from '@contract-management/services/causes-selection.service';\nimport { ContractClassService } from '@contract-management/services/contract-class.service';\nimport { DependencyService } from '@contract-management/services/dependency.service';\nimport { GroupService } from '@contract-management/services/group.service';\nimport { InsuredRisksService } from '@contract-management/services/insured_risks.service';\nimport { ManagementSupportService } from '@contract-management/services/management-support.service';\nimport { TypeWarrantyService } from '@contract-management/services/type_warranty.service';\nimport { Department } from '@shared/models/department.model';\nimport { Municipality } from '@shared/models/municipality.model';\nimport { DepartmentService } from '@shared/services/department.service';\nimport { MunicipalityService } from '@shared/services/municipality.service';\nimport { Supervisor } from '@supervisor-management/models/supervisor.model';\nimport { SupervisorService } from '@supervisor-management/services/supervisor.service';\nimport { NgxCurrencyDirective } from 'ngx-currency';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { ContractType } from '../../models/contract-type.model';\nimport { ContractYear } from '../../models/contract-year.model';\nimport { SelectionModality } from '../../models/selection-modality.model';\nimport { TrackingType } from '../../models/tracking-type.model';\nimport { ContractTypeService } from '../../services/contract-type.service';\nimport { ContractYearService } from '../../services/contract-year.service';\nimport { SelectionModalityService } from '../../services/selection-modality.service';\nimport { StatusService } from '../../services/status.service';\nimport { TrackingTypeService } from '../../services/tracking-type.service';\n\ninterface ContractDetailFormValue {\n  contractNumber: number;\n  contractYear: number;\n  object: string;\n  sigepLink?: string;\n  secopLink?: string;\n  rup: boolean;\n  addition: boolean;\n  cession: boolean;\n  settled: boolean;\n  selectionModality: number;\n  trackingType: number;\n  contractType: number;\n  dependency: number;\n  group: number;\n  earlyTermination: boolean | null;\n  monthlyPayment: number;\n  municipalityId: number;\n  departmentId: number;\n  secopCode: string;\n  warranty: boolean;\n  dateExpeditionWarranty?: string;\n  typeWarrantyId?: number;\n  insuredRisksId?: number;\n  supervisorId: number;\n  supervisorFullName: string;\n  causesSelectionId: number;\n  contractClassId: number;\n  managementSupportId: number;\n}\n\n@Component({\n  selector: 'app-contract-detail-form',\n  templateUrl: './contract-detail-form.component.html',\n  styleUrl: './contract-detail-form.component.scss',\n  standalone: true,\n  imports: [\n    ReactiveFormsModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatSelectModule,\n    MatSlideToggleModule,\n    MatAutocompleteModule,\n    MatIconModule,\n    MatButtonModule,\n    MatDatepickerModule,\n    AsyncPipe,\n    NgxCurrencyDirective,\n  ],\n})\nexport class ContractDetailFormComponent implements OnInit {\n  @ViewChild('departmentAuto') departmentAutocomplete?: MatAutocomplete;\n  @ViewChild('departmentInput', { read: MatAutocompleteTrigger })\n  departmentAutocompleteTrigger?: MatAutocompleteTrigger;\n  @ViewChild('municipalityAuto') municipalityAutocomplete?: MatAutocomplete;\n  @ViewChild('municipalityInput', { read: MatAutocompleteTrigger })\n  municipalityAutocompleteTrigger?: MatAutocompleteTrigger;\n\n  @Input() contract: Contract | null = null;\n  contractForm: FormGroup = this.formBuilder.group({\n    contractNumber: ['', [Validators.required, Validators.min(1)]],\n    contractYear: ['', Validators.required],\n    object: ['', Validators.required],\n    sigepLink: [''],\n    secopLink: [''],\n    rup: [false],\n    addition: [{ value: false, disabled: true }],\n    cession: [{ value: false, disabled: true }],\n    settled: [{ value: false, disabled: true }],\n    selectionModality: ['', Validators.required],\n    trackingType: ['', Validators.required],\n    contractType: ['', Validators.required],\n    dependency: ['', Validators.required],\n    group: ['', Validators.required],\n    earlyTermination: [{ value: null, disabled: true }],\n    monthlyPayment: [null, [Validators.required, Validators.min(1)]],\n    municipalityId: ['', Validators.required],\n    departmentId: ['', Validators.required],\n    secopCode: ['', Validators.required],\n    warranty: [false],\n    dateExpeditionWarranty: [''],\n    typeWarrantyId: [''],\n    insuredRisksId: [''],\n    supervisorId: [null as number | null, Validators.required],\n    supervisorFullName: ['', Validators.required],\n    causesSelectionId: ['', Validators.required],\n    contractClassId: ['', Validators.required],\n    managementSupportId: ['', Validators.required],\n    completed: [false],\n  });\n  @Output() earlyTerminationToggled = new EventEmitter<void>();\n  @Output() contractCompletedChanged = new EventEmitter<boolean>();\n\n  selectionModalities: SelectionModality[] = [];\n  trackingTypes: TrackingType[] = [];\n  contractTypes: ContractType[] = [];\n  contractYears: ContractYear[] = [];\n  typeWarranty: TypeWarranty[] = [];\n  insuredRisks: InsuredRisks[] = [];\n  dependencies: Dependency[] = [];\n  groups: Group[] = [];\n  filteredGroups: Group[] = [];\n  departments: Department[] = [];\n  municipalities: Municipality[] = [];\n  filteredMunicipalities: Municipality[] = [];\n  supervisors: Supervisor[] = [];\n  managementSupport: ManagementSupport[] = [];\n  causesSelection: CausesSelection[] = [];\n  contractClass: ContractClass[] = [];\n\n  supervisor: Supervisor | null = null;\n  supervisorFullNameControl = this.contractForm.get(\n    'supervisorFullName',\n  ) as FormControl;\n  filteredSupervisors = this.supervisorFullNameControl.valueChanges.pipe(\n    startWith(''),\n    map((value) => {\n      const searchValue = typeof value === 'string' ? value.toLowerCase() : '';\n      return this.supervisors.filter((supervisor) =>\n        supervisor.fullName.toLowerCase().includes(searchValue),\n      );\n    }),\n  );\n\n  departmentSearchCtrl = new FormControl('');\n  municipalitySearchCtrl = new FormControl('');\n\n  filteredDepartments: Observable<Department[]>;\n\n  constructor(\n    private readonly selectionModalityService: SelectionModalityService,\n    private readonly trackingTypeService: TrackingTypeService,\n    private readonly contractTypeService: ContractTypeService,\n    private readonly alert: AlertService,\n    private readonly formBuilder: FormBuilder,\n    private readonly contractYearService: ContractYearService,\n    private readonly contractService: ContractService,\n    private readonly supervisorService: SupervisorService,\n    private readonly typeWarrantyService: TypeWarrantyService,\n    private readonly insuredRisksService: InsuredRisksService,\n    private readonly dependencyService: DependencyService,\n    private readonly groupservice: GroupService,\n    private readonly departmentService: DepartmentService,\n    private readonly municipalityService: MunicipalityService,\n    private readonly managementSupportService: ManagementSupportService,\n    private readonly causesSelectionService: CausesSelectionService,\n    private readonly contractClassService: ContractClassService,\n    private readonly statusService: StatusService,\n    private readonly spinner: NgxSpinnerService,\n  ) {\n    this.filteredDepartments = this.departmentSearchCtrl.valueChanges.pipe(\n      startWith(''),\n      map((value) => this._filterDepartments(value || '')),\n    );\n\n    this.filteredMunicipalities = [];\n\n    this.departmentSearchCtrl.valueChanges.subscribe((value) => {\n      if (value) {\n        this.municipalitySearchCtrl.enable();\n      } else {\n        this.municipalitySearchCtrl.disable();\n      }\n    });\n  }\n\n  ngOnInit(): void {\n    this.loadData();\n    this.setupContractNumberValidation();\n    this.setupWarrantyValidation();\n\n    if (!this.departmentSearchCtrl.value) {\n      this.municipalitySearchCtrl.disable();\n    }\n\n    if (this.contract) {\n      this.contractForm.patchValue({\n        ...this.contract,\n        contractYear: this.contract.contractYear?.id,\n        selectionModality: this.contract.selectionModality?.id,\n        trackingType: this.contract.trackingType?.id,\n        contractType: this.contract.contractType?.id,\n        dependency: this.contract.dependency?.id,\n        group: this.contract.group?.id,\n        municipalityId: this.contract.municipality?.id,\n        departmentId: this.contract.department?.id,\n        causesSelectionId: this.contract.causesSelection?.id,\n        contractClassId: this.contract.contractClass?.id,\n        managementSupportId: this.contract.managementSupport?.id,\n      });\n\n      // Set supervisor data if available\n      if (this.contract.supervisor) {\n        this.supervisor = this.contract.supervisor;\n        this.contractForm.patchValue({\n          supervisorId: this.contract.supervisor.id,\n          supervisorFullName: this.contract.supervisor.fullName,\n        });\n      }\n\n      const department = this.contract.department;\n      if (department) {\n        this.departmentSearchCtrl.setValue(department.name);\n        if (this.contract.municipality) {\n          this.municipalitySearchCtrl.setValue(this.contract.municipality.name);\n        }\n      }\n\n      if (!this.contract.earlyTermination) {\n        this.contractForm.get('earlyTermination')?.enable();\n      }\n\n      if (this.contract.status) {\n        const isFinished = this.contract.status.name === 'FINALIZADO';\n        this.contractForm.get('completed')?.setValue(isFinished);\n\n        if (isFinished) {\n          this.setFormControlsState(false);\n        }\n      }\n    }\n  }\n\n  private loadData(): void {\n    forkJoin({\n      selectionModalities: this.selectionModalityService.getAll(),\n      trackingTypes: this.trackingTypeService.getAll(),\n      contractTypes: this.contractTypeService.getAll(),\n      contractYears: this.contractYearService.getAll(),\n      typeWarranty: this.typeWarrantyService.getAll(),\n      insuredRisks: this.insuredRisksService.getAll(),\n      dependencies: this.dependencyService.getAll(),\n      groups: this.groupservice.getAll(),\n      department: this.departmentService.getAll(),\n      municipality: this.municipalityService.getAll(),\n      supervisors: this.supervisorService.getAll(),\n      managementSupport: this.managementSupportService.getAll(),\n      causesSelection: this.causesSelectionService.getAll(),\n      contractClass: this.contractClassService.getAll(),\n    }).subscribe({\n      next: ({\n        selectionModalities,\n        trackingTypes,\n        contractTypes,\n        contractYears,\n        typeWarranty,\n        insuredRisks,\n        dependencies,\n        groups,\n        department,\n        municipality,\n        supervisors,\n        managementSupport,\n        causesSelection,\n        contractClass,\n      }) => {\n        this.selectionModalities = selectionModalities;\n        this.trackingTypes = trackingTypes;\n        this.contractTypes = contractTypes;\n        this.contractYears = contractYears;\n        this.typeWarranty = typeWarranty;\n        this.insuredRisks = insuredRisks;\n        this.dependencies = dependencies;\n        this.groups = groups;\n        this.departments = department;\n        this.municipalities = municipality;\n        this.supervisors = supervisors;\n        this.managementSupport = managementSupport;\n        this.causesSelection = causesSelection;\n        this.contractClass = contractClass;\n\n        if (!this.contract) {\n          const currentYear = new Date().getFullYear();\n          const defaultYear = this.contractYears.find(\n            (y) => y.year === currentYear,\n          );\n          if (defaultYear) {\n            this.contractForm.get('contractYear')?.setValue(defaultYear.id);\n          }\n        }\n\n        if (this.contract?.dependency?.id) {\n          this.onDependencyChange(this.contract.dependency.id);\n        }\n        if (this.contract?.department?.id) {\n          this.onDepartmentChange(this.contract.department.id);\n        }\n\n        this.filteredDepartments = this.departmentSearchCtrl.valueChanges.pipe(\n          startWith(''),\n          map((value) => this._filterDepartments(value || '')),\n        );\n      },\n      error: (error) => {\n        this.alert.error(error.error?.detail ?? 'Error al cargar los datos');\n      },\n    });\n  }\n\n  onDependencyChange(dependencyId: number): void {\n    if (dependencyId != null) {\n      this.filteredGroups = this.groups.filter(\n        (group) => group.dependency.id === dependencyId,\n      );\n    } else {\n      this.filteredGroups = [];\n    }\n  }\n\n  onDepartmentChange(departmentId: number): void {\n    if (departmentId != null) {\n      this.municipalitySearchCtrl.enable();\n      this.loadMunicipalities(departmentId);\n    } else {\n      this.filteredMunicipalities = [];\n      this.municipalitySearchCtrl.setValue('');\n      this.municipalitySearchCtrl.disable();\n      this.contractForm.get('municipalityId')?.setValue(null);\n    }\n  }\n\n  loadMunicipalities(departmentId: number): void {\n    this.municipalityService.getAllByDepartmentId(departmentId).subscribe({\n      next: (municipalities) => {\n        this.municipalities = municipalities;\n        this.filteredMunicipalities = this.municipalities;\n\n        this.municipalitySearchCtrl.valueChanges\n          .pipe(\n            startWith(''),\n            map((value) => this._filterMunicipalities(value || '')),\n          )\n          .subscribe((filtered) => {\n            this.filteredMunicipalities = filtered;\n          });\n      },\n      error: (error) => {\n        this.alert.error(error.error?.detail ?? 'Error al cargar municipios');\n      },\n    });\n  }\n\n  handleDepartmentSelection(event: MatAutocompleteSelectedEvent): void {\n    const selectedDepartmentName = event.option.viewValue;\n    const selectedDepartment = this.departments.find(\n      (dept) => dept.name === selectedDepartmentName,\n    );\n\n    if (selectedDepartment) {\n      this.contractForm.get('departmentId')?.setValue(selectedDepartment.id);\n      this.contractForm.get('departmentId')?.markAsTouched();\n      this.contractForm.get('municipalityId')?.setValue(null);\n      this.contractForm.get('municipalityId')?.markAsTouched();\n      this.municipalitySearchCtrl.setValue('');\n      this.onDepartmentChange(selectedDepartment.id);\n\n      setTimeout(() => {\n        this.departmentAutocompleteTrigger?.closePanel();\n      }, 0);\n    }\n  }\n\n  handleMunicipalitySelection(event: MatAutocompleteSelectedEvent): void {\n    const selectedMunicipalityName = event.option.viewValue;\n    const selectedMunicipality = this.municipalities.find(\n      (mun) => mun.name === selectedMunicipalityName,\n    );\n\n    if (selectedMunicipality) {\n      this.contractForm\n        .get('municipalityId')\n        ?.setValue(selectedMunicipality.id);\n      this.contractForm.get('municipalityId')?.markAsTouched();\n\n      setTimeout(() => {\n        this.municipalityAutocompleteTrigger?.closePanel();\n      }, 0);\n    }\n  }\n\n  private _filterDepartments(value: string): Department[] {\n    const filterValue = value.toLowerCase();\n    return this.departments.filter((department) =>\n      department.name.toLowerCase().includes(filterValue),\n    );\n  }\n\n  private _filterMunicipalities(value: string): Municipality[] {\n    const filterValue = value.toLowerCase();\n    return this.municipalities.filter((municipality) =>\n      municipality.name.toLowerCase().includes(filterValue),\n    );\n  }\n\n  displayDepartment(departmentName: string): string {\n    return departmentName;\n  }\n\n  displayMunicipality(municipalityName: string): string {\n    return municipalityName;\n  }\n\n  displaySupervisorName(supervisor: Supervisor | string): string {\n    if (typeof supervisor === 'string') {\n      return supervisor;\n    }\n    return supervisor ? supervisor.fullName : '';\n  }\n\n  showAllDepartments(): void {\n    if (!this.departmentAutocompleteTrigger?.panelOpen) {\n      this.departmentSearchCtrl.setValue('');\n      this.contractForm.get('departmentId')?.markAsTouched();\n      setTimeout(() => {\n        this.departmentAutocompleteTrigger?.openPanel();\n      }, 0);\n    }\n  }\n\n  showAllMunicipalities(): void {\n    if (\n      this.departmentSearchCtrl.value &&\n      !this.municipalityAutocompleteTrigger?.panelOpen\n    ) {\n      this.municipalitySearchCtrl.setValue('');\n      this.contractForm.get('municipalityId')?.markAsTouched();\n      setTimeout(() => {\n        this.municipalityAutocompleteTrigger?.openPanel();\n      }, 0);\n    }\n  }\n\n  async onRupChange(event: MatSlideToggleChange): Promise<void> {\n    if (!this.contract?.id) return;\n\n    if (event.checked) {\n      const confirmed = await this.alert.confirm('¿Está seguro de marcar RUP?');\n      if (confirmed) {\n        this.spinner.show();\n        this.contractService\n          .update(this.contract.id, { rup: true })\n          .pipe(finalize(() => this.spinner.hide()))\n          .subscribe({\n            next: () => {\n              this.contractForm.get('rup')?.setValue(true);\n              this.alert.success('El contrato ha sido marcado como RUP.');\n            },\n            error: (error) => {\n              this.contractForm.get('rup')?.setValue(false);\n              this.alert.error(\n                error.error?.detail ?? 'Error al actualizar el contrato',\n              );\n            },\n          });\n      } else {\n        this.contractForm.get('rup')?.setValue(false);\n      }\n    } else {\n      const confirmed = await this.alert.confirm(\n        '¿Está seguro de desmarcar RUP?',\n      );\n      if (confirmed) {\n        this.spinner.show();\n        this.contractService\n          .update(this.contract.id, { rup: false })\n          .pipe(finalize(() => this.spinner.hide()))\n          .subscribe({\n            next: () => {\n              this.contractForm.get('rup')?.setValue(false);\n              this.alert.success('El contrato ha sido desmarcado como RUP.');\n            },\n            error: (error) => {\n              this.contractForm.get('rup')?.setValue(true);\n              this.alert.error(\n                error.error?.detail ?? 'Error al actualizar el contrato',\n              );\n            },\n          });\n      } else {\n        this.contractForm.get('rup')?.setValue(true);\n      }\n    }\n  }\n\n  async onCompletedChange(event: MatSlideToggleChange): Promise<void> {\n    if (!this.contract?.id) return;\n\n    if (event.checked) {\n      const confirmed = await this.alert.confirm(\n        '¿Está seguro de marcar el contrato como finalizado?',\n      );\n      if (confirmed) {\n        this.spinner.show();\n        this.statusService\n          .getByName('FINALIZADO')\n          .pipe(\n            switchMap((status) =>\n              this.contractService.update(this.contract!.id, {\n                statusId: status.id,\n              }),\n            ),\n            finalize(() => this.spinner.hide()),\n          )\n          .subscribe({\n            next: () => {\n              this.contractForm.get('completed')?.setValue(true);\n              this.setFormControlsState(false);\n              this.alert.success(\n                'El contrato ha sido marcado como finalizado.',\n              );\n              this.contractCompletedChanged.emit(true);\n            },\n            error: (error) => {\n              this.contractForm.get('completed')?.setValue(false);\n              this.alert.error(\n                error.error?.detail ??\n                  'Error al actualizar el estado del contrato',\n              );\n            },\n          });\n      } else {\n        this.contractForm.get('completed')?.setValue(false);\n      }\n    } else {\n      const confirmed = await this.alert.confirm(\n        '¿Está seguro de desmarcar el contrato como finalizado?',\n      );\n      if (confirmed) {\n        this.spinner.show();\n        this.statusService\n          .getByName('EN EJECUCIÓN')\n          .pipe(\n            switchMap((status) =>\n              this.contractService.update(this.contract!.id, {\n                statusId: status.id,\n              }),\n            ),\n            finalize(() => this.spinner.hide()),\n          )\n          .subscribe({\n            next: () => {\n              this.contractForm.get('completed')?.setValue(false);\n              this.setFormControlsState(true);\n              this.alert.success(\n                'El contrato ha sido marcado como en ejecución.',\n              );\n              this.contractCompletedChanged.emit(false);\n            },\n            error: (error) => {\n              this.contractForm.get('completed')?.setValue(true);\n              this.alert.error(\n                error.error?.detail ??\n                  'Error al actualizar el estado del contrato',\n              );\n            },\n          });\n      } else {\n        this.contractForm.get('completed')?.setValue(true);\n      }\n    }\n  }\n\n  private setFormControlsState(enable: boolean): void {\n    const excludedControls = ['completed'];\n\n    Object.keys(this.contractForm.controls).forEach((controlName) => {\n      if (!excludedControls.includes(controlName)) {\n        const control = this.contractForm.get(controlName);\n        if (enable) {\n          control?.enable();\n        } else {\n          control?.disable();\n        }\n      }\n    });\n  }\n\n  private setupContractNumberValidation(): void {\n    this.contractForm\n      .get('contractNumber')\n      ?.valueChanges.pipe(\n        debounceTime(300),\n        switchMap((contractNumber) => {\n          const contractYearId = this.contractForm.get('contractYear')?.value;\n          if (contractNumber && contractYearId) {\n            return this.contractService.validateContractNumber(\n              contractNumber,\n              contractYearId,\n            );\n          }\n          return of(true);\n        }),\n      )\n      .subscribe((isValid) => {\n        if (!isValid) {\n          this.contractForm\n            .get('contractNumber')\n            ?.setErrors({ duplicateContract: true });\n        }\n      });\n\n    this.contractForm.get('contractYear')?.valueChanges.subscribe(() => {\n      this.contractForm.get('contractNumber')?.updateValueAndValidity();\n    });\n  }\n\n  onSupervisorSelected(event: MatAutocompleteSelectedEvent): void {\n    const supervisor = event.option.value as Supervisor;\n    this.supervisor = supervisor;\n    this.contractForm.patchValue({\n      supervisorId: supervisor.id,\n      supervisorFullName: supervisor.fullName,\n    });\n  }\n\n  clearSupervisor(): void {\n    this.supervisor = null;\n    this.contractForm.patchValue({\n      supervisorId: null,\n      supervisorFullName: '',\n    });\n  }\n\n  private setupWarrantyValidation(): void {\n    const dateExpeditionWarranty = this.contractForm.get(\n      'dateExpeditionWarranty',\n    );\n    const typeWarrantyId = this.contractForm.get('typeWarrantyId');\n    const insuredRisksId = this.contractForm.get('insuredRisksId');\n\n    const updateValidation = (hasWarranty: boolean) => {\n      if (hasWarranty) {\n        dateExpeditionWarranty?.setValidators([Validators.required]);\n        typeWarrantyId?.setValidators([Validators.required]);\n        insuredRisksId?.setValidators([Validators.required]);\n      } else {\n        dateExpeditionWarranty?.setValidators(null);\n        typeWarrantyId?.setValidators(null);\n        insuredRisksId?.setValidators(null);\n\n        dateExpeditionWarranty?.patchValue(null, { emitEvent: false });\n        typeWarrantyId?.patchValue(null, { emitEvent: false });\n        insuredRisksId?.patchValue(null, { emitEvent: false });\n      }\n\n      dateExpeditionWarranty?.markAsUntouched();\n      typeWarrantyId?.markAsUntouched();\n      insuredRisksId?.markAsUntouched();\n\n      dateExpeditionWarranty?.updateValueAndValidity({ emitEvent: false });\n      typeWarrantyId?.updateValueAndValidity({ emitEvent: false });\n      insuredRisksId?.updateValueAndValidity({ emitEvent: false });\n    };\n\n    const initialWarranty = this.contractForm.get('warranty')?.value || false;\n    updateValidation(initialWarranty);\n\n    this.contractForm.get('warranty')?.valueChanges.subscribe(updateValidation);\n  }\n\n  isValid(): boolean {\n    return this.contractForm.valid;\n  }\n\n  getValue(): ContractDetailFormValue {\n    Object.keys(this.contractForm.controls).forEach((key) => {\n      const control = this.contractForm.get(key);\n      if (control) {\n        control.markAsTouched();\n      }\n    });\n\n    return this.contractForm.getRawValue();\n  }\n}\n"], "mappings": ";;;;AAAA,SAASA,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,QAAQ,EAAcC,EAAE,QAAQ,MAAM;AAC/C,SACEC,YAAY,EACZC,QAAQ,EACRC,GAAG,EACHC,SAAS,EACTC,SAAS,QACJ,gBAAgB;AACvB,SAASC,eAAe,QAAQ,iCAAiC;AAEjE,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SACEC,SAAS,EACTC,YAAY,EACZC,KAAK,EAELC,MAAM,EACNC,SAAS,QACJ,eAAe;AACtB,SACEC,WAAW,EACXC,WAAW,EAEXC,mBAAmB,EACnBC,UAAU,QACL,gBAAgB;AACvB,SAEEC,oBAAoB,QACf,gCAAgC;AAEvC,SAEEC,qBAAqB,EAErBC,sBAAsB,QACjB,gCAAgC;AACvC,SAASC,mBAAmB,QAAQ,8BAA8B;AAElE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAS1D,SAASC,sBAAsB,QAAQ,wDAAwD;AAC/F,SAASC,oBAAoB,QAAQ,sDAAsD;AAC3F,SAASC,iBAAiB,QAAQ,kDAAkD;AACpF,SAASC,YAAY,QAAQ,6CAA6C;AAC1E,SAASC,mBAAmB,QAAQ,qDAAqD;AACzF,SAASC,wBAAwB,QAAQ,0DAA0D;AACnG,SAASC,mBAAmB,QAAQ,qDAAqD;AAGzF,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,mBAAmB,QAAQ,uCAAuC;AAE3E,SAASC,iBAAiB,QAAQ,oDAAoD;AACtF,SAASC,oBAAoB,QAAQ,cAAc;AACnD,SAASC,iBAAiB,QAAQ,aAAa;AAK/C,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,SAASC,wBAAwB,QAAQ,2CAA2C;AACpF,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,SAASC,mBAAmB,QAAQ,sCAAsC;AAoDnE,IAAMC,2BAA2B,GAAjC,MAAMA,2BAA2B;EA+EtCC,YACmBC,wBAAkD,EAClDC,mBAAwC,EACxCC,mBAAwC,EACxCC,KAAmB,EACnBC,WAAwB,EACxBC,mBAAwC,EACxCC,eAAgC,EAChCC,iBAAoC,EACpCC,mBAAwC,EACxCC,mBAAwC,EACxCC,iBAAoC,EACpCC,YAA0B,EAC1BC,iBAAoC,EACpCC,mBAAwC,EACxCC,wBAAkD,EAClDC,sBAA8C,EAC9CC,oBAA0C,EAC1CC,aAA4B,EAC5BC,OAA0B;IAlB1B,KAAAlB,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IA1FjB,KAAAC,QAAQ,GAAoB,IAAI;IACzC,KAAAC,YAAY,GAAc,IAAI,CAAChB,WAAW,CAACiB,KAAK,CAAC;MAC/CC,cAAc,EAAE,CAAC,EAAE,EAAE,CAACnD,UAAU,CAACoD,QAAQ,EAAEpD,UAAU,CAACqD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,YAAY,EAAE,CAAC,EAAE,EAAEtD,UAAU,CAACoD,QAAQ,CAAC;MACvCG,MAAM,EAAE,CAAC,EAAE,EAAEvD,UAAU,CAACoD,QAAQ,CAAC;MACjCI,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,GAAG,EAAE,CAAC,KAAK,CAAC;MACZC,QAAQ,EAAE,CAAC;QAAEC,KAAK,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC5CC,OAAO,EAAE,CAAC;QAAEF,KAAK,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC3CE,OAAO,EAAE,CAAC;QAAEH,KAAK,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC3CG,iBAAiB,EAAE,CAAC,EAAE,EAAEhE,UAAU,CAACoD,QAAQ,CAAC;MAC5Ca,YAAY,EAAE,CAAC,EAAE,EAAEjE,UAAU,CAACoD,QAAQ,CAAC;MACvCc,YAAY,EAAE,CAAC,EAAE,EAAElE,UAAU,CAACoD,QAAQ,CAAC;MACvCe,UAAU,EAAE,CAAC,EAAE,EAAEnE,UAAU,CAACoD,QAAQ,CAAC;MACrCF,KAAK,EAAE,CAAC,EAAE,EAAElD,UAAU,CAACoD,QAAQ,CAAC;MAChCgB,gBAAgB,EAAE,CAAC;QAAER,KAAK,EAAE,IAAI;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACnDQ,cAAc,EAAE,CAAC,IAAI,EAAE,CAACrE,UAAU,CAACoD,QAAQ,EAAEpD,UAAU,CAACqD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAChEiB,cAAc,EAAE,CAAC,EAAE,EAAEtE,UAAU,CAACoD,QAAQ,CAAC;MACzCmB,YAAY,EAAE,CAAC,EAAE,EAAEvE,UAAU,CAACoD,QAAQ,CAAC;MACvCoB,SAAS,EAAE,CAAC,EAAE,EAAExE,UAAU,CAACoD,QAAQ,CAAC;MACpCqB,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjBC,sBAAsB,EAAE,CAAC,EAAE,CAAC;MAC5BC,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,YAAY,EAAE,CAAC,IAAqB,EAAE7E,UAAU,CAACoD,QAAQ,CAAC;MAC1D0B,kBAAkB,EAAE,CAAC,EAAE,EAAE9E,UAAU,CAACoD,QAAQ,CAAC;MAC7C2B,iBAAiB,EAAE,CAAC,EAAE,EAAE/E,UAAU,CAACoD,QAAQ,CAAC;MAC5C4B,eAAe,EAAE,CAAC,EAAE,EAAEhF,UAAU,CAACoD,QAAQ,CAAC;MAC1C6B,mBAAmB,EAAE,CAAC,EAAE,EAAEjF,UAAU,CAACoD,QAAQ,CAAC;MAC9C8B,SAAS,EAAE,CAAC,KAAK;KAClB,CAAC;IACQ,KAAAC,uBAAuB,GAAG,IAAI1F,YAAY,EAAQ;IAClD,KAAA2F,wBAAwB,GAAG,IAAI3F,YAAY,EAAW;IAEhE,KAAA4F,mBAAmB,GAAwB,EAAE;IAC7C,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAC,YAAY,GAAmB,EAAE;IACjC,KAAAC,YAAY,GAAmB,EAAE;IACjC,KAAAC,YAAY,GAAiB,EAAE;IAC/B,KAAAC,MAAM,GAAY,EAAE;IACpB,KAAAC,cAAc,GAAY,EAAE;IAC5B,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,cAAc,GAAmB,EAAE;IACnC,KAAAC,sBAAsB,GAAmB,EAAE;IAC3C,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,iBAAiB,GAAwB,EAAE;IAC3C,KAAAC,eAAe,GAAsB,EAAE;IACvC,KAAAC,aAAa,GAAoB,EAAE;IAEnC,KAAAC,UAAU,GAAsB,IAAI;IACpC,KAAAC,yBAAyB,GAAG,IAAI,CAACrD,YAAY,CAACsD,GAAG,CAC/C,oBAAoB,CACN;IAChB,KAAAC,mBAAmB,GAAG,IAAI,CAACF,yBAAyB,CAACG,YAAY,CAACC,IAAI,CACpEtH,SAAS,CAAC,EAAE,CAAC,EACbD,GAAG,CAAEyE,KAAK,IAAI;MACZ,MAAM+C,WAAW,GAAG,OAAO/C,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACgD,WAAW,EAAE,GAAG,EAAE;MACxE,OAAO,IAAI,CAACX,WAAW,CAACY,MAAM,CAAER,UAAU,IACxCA,UAAU,CAACS,QAAQ,CAACF,WAAW,EAAE,CAACG,QAAQ,CAACJ,WAAW,CAAC,CACxD;IACH,CAAC,CAAC,CACH;IAED,KAAAK,oBAAoB,GAAG,IAAIlH,WAAW,CAAC,EAAE,CAAC;IAC1C,KAAAmH,sBAAsB,GAAG,IAAInH,WAAW,CAAC,EAAE,CAAC;IAyB1C,IAAI,CAACoH,mBAAmB,GAAG,IAAI,CAACF,oBAAoB,CAACP,YAAY,CAACC,IAAI,CACpEtH,SAAS,CAAC,EAAE,CAAC,EACbD,GAAG,CAAEyE,KAAK,IAAK,IAAI,CAACuD,kBAAkB,CAACvD,KAAK,IAAI,EAAE,CAAC,CAAC,CACrD;IAED,IAAI,CAACoC,sBAAsB,GAAG,EAAE;IAEhC,IAAI,CAACgB,oBAAoB,CAACP,YAAY,CAACW,SAAS,CAAExD,KAAK,IAAI;MACzD,IAAIA,KAAK,EAAE;QACT,IAAI,CAACqD,sBAAsB,CAACI,MAAM,EAAE;MACtC,CAAC,MAAM;QACL,IAAI,CAACJ,sBAAsB,CAACK,OAAO,EAAE;MACvC;IACF,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACC,6BAA6B,EAAE;IACpC,IAAI,CAACC,uBAAuB,EAAE;IAE9B,IAAI,CAAC,IAAI,CAACV,oBAAoB,CAACpD,KAAK,EAAE;MACpC,IAAI,CAACqD,sBAAsB,CAACK,OAAO,EAAE;IACvC;IAEA,IAAI,IAAI,CAACtE,QAAQ,EAAE;MACjB,IAAI,CAACC,YAAY,CAAC0E,UAAU,CAAC;QAC3B,GAAG,IAAI,CAAC3E,QAAQ;QAChBM,YAAY,EAAE,IAAI,CAACN,QAAQ,CAACM,YAAY,EAAEsE,EAAE;QAC5C5D,iBAAiB,EAAE,IAAI,CAAChB,QAAQ,CAACgB,iBAAiB,EAAE4D,EAAE;QACtD3D,YAAY,EAAE,IAAI,CAACjB,QAAQ,CAACiB,YAAY,EAAE2D,EAAE;QAC5C1D,YAAY,EAAE,IAAI,CAAClB,QAAQ,CAACkB,YAAY,EAAE0D,EAAE;QAC5CzD,UAAU,EAAE,IAAI,CAACnB,QAAQ,CAACmB,UAAU,EAAEyD,EAAE;QACxC1E,KAAK,EAAE,IAAI,CAACF,QAAQ,CAACE,KAAK,EAAE0E,EAAE;QAC9BtD,cAAc,EAAE,IAAI,CAACtB,QAAQ,CAAC6E,YAAY,EAAED,EAAE;QAC9CrD,YAAY,EAAE,IAAI,CAACvB,QAAQ,CAAC8E,UAAU,EAAEF,EAAE;QAC1C7C,iBAAiB,EAAE,IAAI,CAAC/B,QAAQ,CAACmD,eAAe,EAAEyB,EAAE;QACpD5C,eAAe,EAAE,IAAI,CAAChC,QAAQ,CAACoD,aAAa,EAAEwB,EAAE;QAChD3C,mBAAmB,EAAE,IAAI,CAACjC,QAAQ,CAACkD,iBAAiB,EAAE0B;OACvD,CAAC;MAEF;MACA,IAAI,IAAI,CAAC5E,QAAQ,CAACqD,UAAU,EAAE;QAC5B,IAAI,CAACA,UAAU,GAAG,IAAI,CAACrD,QAAQ,CAACqD,UAAU;QAC1C,IAAI,CAACpD,YAAY,CAAC0E,UAAU,CAAC;UAC3B9C,YAAY,EAAE,IAAI,CAAC7B,QAAQ,CAACqD,UAAU,CAACuB,EAAE;UACzC9C,kBAAkB,EAAE,IAAI,CAAC9B,QAAQ,CAACqD,UAAU,CAACS;SAC9C,CAAC;MACJ;MAEA,MAAMgB,UAAU,GAAG,IAAI,CAAC9E,QAAQ,CAAC8E,UAAU;MAC3C,IAAIA,UAAU,EAAE;QACd,IAAI,CAACd,oBAAoB,CAACe,QAAQ,CAACD,UAAU,CAACE,IAAI,CAAC;QACnD,IAAI,IAAI,CAAChF,QAAQ,CAAC6E,YAAY,EAAE;UAC9B,IAAI,CAACZ,sBAAsB,CAACc,QAAQ,CAAC,IAAI,CAAC/E,QAAQ,CAAC6E,YAAY,CAACG,IAAI,CAAC;QACvE;MACF;MAEA,IAAI,CAAC,IAAI,CAAChF,QAAQ,CAACoB,gBAAgB,EAAE;QACnC,IAAI,CAACnB,YAAY,CAACsD,GAAG,CAAC,kBAAkB,CAAC,EAAEc,MAAM,EAAE;MACrD;MAEA,IAAI,IAAI,CAACrE,QAAQ,CAACiF,MAAM,EAAE;QACxB,MAAMC,UAAU,GAAG,IAAI,CAAClF,QAAQ,CAACiF,MAAM,CAACD,IAAI,KAAK,YAAY;QAC7D,IAAI,CAAC/E,YAAY,CAACsD,GAAG,CAAC,WAAW,CAAC,EAAEwB,QAAQ,CAACG,UAAU,CAAC;QAExD,IAAIA,UAAU,EAAE;UACd,IAAI,CAACC,oBAAoB,CAAC,KAAK,CAAC;QAClC;MACF;IACF;EACF;EAEQX,QAAQA,CAAA;IACdzI,QAAQ,CAAC;MACPsG,mBAAmB,EAAE,IAAI,CAACxD,wBAAwB,CAACuG,MAAM,EAAE;MAC3D9C,aAAa,EAAE,IAAI,CAACxD,mBAAmB,CAACsG,MAAM,EAAE;MAChD7C,aAAa,EAAE,IAAI,CAACxD,mBAAmB,CAACqG,MAAM,EAAE;MAChD5C,aAAa,EAAE,IAAI,CAACtD,mBAAmB,CAACkG,MAAM,EAAE;MAChD3C,YAAY,EAAE,IAAI,CAACpD,mBAAmB,CAAC+F,MAAM,EAAE;MAC/C1C,YAAY,EAAE,IAAI,CAACpD,mBAAmB,CAAC8F,MAAM,EAAE;MAC/CzC,YAAY,EAAE,IAAI,CAACpD,iBAAiB,CAAC6F,MAAM,EAAE;MAC7CxC,MAAM,EAAE,IAAI,CAACpD,YAAY,CAAC4F,MAAM,EAAE;MAClCN,UAAU,EAAE,IAAI,CAACrF,iBAAiB,CAAC2F,MAAM,EAAE;MAC3CP,YAAY,EAAE,IAAI,CAACnF,mBAAmB,CAAC0F,MAAM,EAAE;MAC/CnC,WAAW,EAAE,IAAI,CAAC7D,iBAAiB,CAACgG,MAAM,EAAE;MAC5ClC,iBAAiB,EAAE,IAAI,CAACvD,wBAAwB,CAACyF,MAAM,EAAE;MACzDjC,eAAe,EAAE,IAAI,CAACvD,sBAAsB,CAACwF,MAAM,EAAE;MACrDhC,aAAa,EAAE,IAAI,CAACvD,oBAAoB,CAACuF,MAAM;KAChD,CAAC,CAAChB,SAAS,CAAC;MACXiB,IAAI,EAAEA,CAAC;QACLhD,mBAAmB;QACnBC,aAAa;QACbC,aAAa;QACbC,aAAa;QACbC,YAAY;QACZC,YAAY;QACZC,YAAY;QACZC,MAAM;QACNkC,UAAU;QACVD,YAAY;QACZ5B,WAAW;QACXC,iBAAiB;QACjBC,eAAe;QACfC;MAAa,CACd,KAAI;QACH,IAAI,CAACf,mBAAmB,GAAGA,mBAAmB;QAC9C,IAAI,CAACC,aAAa,GAAGA,aAAa;QAClC,IAAI,CAACC,aAAa,GAAGA,aAAa;QAClC,IAAI,CAACC,aAAa,GAAGA,aAAa;QAClC,IAAI,CAACC,YAAY,GAAGA,YAAY;QAChC,IAAI,CAACC,YAAY,GAAGA,YAAY;QAChC,IAAI,CAACC,YAAY,GAAGA,YAAY;QAChC,IAAI,CAACC,MAAM,GAAGA,MAAM;QACpB,IAAI,CAACE,WAAW,GAAGgC,UAAU;QAC7B,IAAI,CAAC/B,cAAc,GAAG8B,YAAY;QAClC,IAAI,CAAC5B,WAAW,GAAGA,WAAW;QAC9B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;QAC1C,IAAI,CAACC,eAAe,GAAGA,eAAe;QACtC,IAAI,CAACC,aAAa,GAAGA,aAAa;QAElC,IAAI,CAAC,IAAI,CAACpD,QAAQ,EAAE;UAClB,MAAMsF,WAAW,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;UAC5C,MAAMC,WAAW,GAAG,IAAI,CAACjD,aAAa,CAACkD,IAAI,CACxCC,CAAC,IAAKA,CAAC,CAACC,IAAI,KAAKN,WAAW,CAC9B;UACD,IAAIG,WAAW,EAAE;YACf,IAAI,CAACxF,YAAY,CAACsD,GAAG,CAAC,cAAc,CAAC,EAAEwB,QAAQ,CAACU,WAAW,CAACb,EAAE,CAAC;UACjE;QACF;QAEA,IAAI,IAAI,CAAC5E,QAAQ,EAAEmB,UAAU,EAAEyD,EAAE,EAAE;UACjC,IAAI,CAACiB,kBAAkB,CAAC,IAAI,CAAC7F,QAAQ,CAACmB,UAAU,CAACyD,EAAE,CAAC;QACtD;QACA,IAAI,IAAI,CAAC5E,QAAQ,EAAE8E,UAAU,EAAEF,EAAE,EAAE;UACjC,IAAI,CAACkB,kBAAkB,CAAC,IAAI,CAAC9F,QAAQ,CAAC8E,UAAU,CAACF,EAAE,CAAC;QACtD;QAEA,IAAI,CAACV,mBAAmB,GAAG,IAAI,CAACF,oBAAoB,CAACP,YAAY,CAACC,IAAI,CACpEtH,SAAS,CAAC,EAAE,CAAC,EACbD,GAAG,CAAEyE,KAAK,IAAK,IAAI,CAACuD,kBAAkB,CAACvD,KAAK,IAAI,EAAE,CAAC,CAAC,CACrD;MACH,CAAC;MACDmF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC/G,KAAK,CAAC+G,KAAK,CAACA,KAAK,CAACA,KAAK,EAAEC,MAAM,IAAI,2BAA2B,CAAC;MACtE;KACD,CAAC;EACJ;EAEAH,kBAAkBA,CAACI,YAAoB;IACrC,IAAIA,YAAY,IAAI,IAAI,EAAE;MACxB,IAAI,CAACpD,cAAc,GAAG,IAAI,CAACD,MAAM,CAACiB,MAAM,CACrC3D,KAAK,IAAKA,KAAK,CAACiB,UAAU,CAACyD,EAAE,KAAKqB,YAAY,CAChD;IACH,CAAC,MAAM;MACL,IAAI,CAACpD,cAAc,GAAG,EAAE;IAC1B;EACF;EAEAiD,kBAAkBA,CAACvE,YAAoB;IACrC,IAAIA,YAAY,IAAI,IAAI,EAAE;MACxB,IAAI,CAAC0C,sBAAsB,CAACI,MAAM,EAAE;MACpC,IAAI,CAAC6B,kBAAkB,CAAC3E,YAAY,CAAC;IACvC,CAAC,MAAM;MACL,IAAI,CAACyB,sBAAsB,GAAG,EAAE;MAChC,IAAI,CAACiB,sBAAsB,CAACc,QAAQ,CAAC,EAAE,CAAC;MACxC,IAAI,CAACd,sBAAsB,CAACK,OAAO,EAAE;MACrC,IAAI,CAACrE,YAAY,CAACsD,GAAG,CAAC,gBAAgB,CAAC,EAAEwB,QAAQ,CAAC,IAAI,CAAC;IACzD;EACF;EAEAmB,kBAAkBA,CAAC3E,YAAoB;IACrC,IAAI,CAAC7B,mBAAmB,CAACyG,oBAAoB,CAAC5E,YAAY,CAAC,CAAC6C,SAAS,CAAC;MACpEiB,IAAI,EAAGtC,cAAc,IAAI;QACvB,IAAI,CAACA,cAAc,GAAGA,cAAc;QACpC,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAACD,cAAc;QAEjD,IAAI,CAACkB,sBAAsB,CAACR,YAAY,CACrCC,IAAI,CACHtH,SAAS,CAAC,EAAE,CAAC,EACbD,GAAG,CAAEyE,KAAK,IAAK,IAAI,CAACwF,qBAAqB,CAACxF,KAAK,IAAI,EAAE,CAAC,CAAC,CACxD,CACAwD,SAAS,CAAEiC,QAAQ,IAAI;UACtB,IAAI,CAACrD,sBAAsB,GAAGqD,QAAQ;QACxC,CAAC,CAAC;MACN,CAAC;MACDN,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC/G,KAAK,CAAC+G,KAAK,CAACA,KAAK,CAACA,KAAK,EAAEC,MAAM,IAAI,4BAA4B,CAAC;MACvE;KACD,CAAC;EACJ;EAEAM,yBAAyBA,CAACC,KAAmC;IAC3D,MAAMC,sBAAsB,GAAGD,KAAK,CAACE,MAAM,CAACC,SAAS;IACrD,MAAMC,kBAAkB,GAAG,IAAI,CAAC7D,WAAW,CAAC4C,IAAI,CAC7CkB,IAAI,IAAKA,IAAI,CAAC5B,IAAI,KAAKwB,sBAAsB,CAC/C;IAED,IAAIG,kBAAkB,EAAE;MACtB,IAAI,CAAC1G,YAAY,CAACsD,GAAG,CAAC,cAAc,CAAC,EAAEwB,QAAQ,CAAC4B,kBAAkB,CAAC/B,EAAE,CAAC;MACtE,IAAI,CAAC3E,YAAY,CAACsD,GAAG,CAAC,cAAc,CAAC,EAAEsD,aAAa,EAAE;MACtD,IAAI,CAAC5G,YAAY,CAACsD,GAAG,CAAC,gBAAgB,CAAC,EAAEwB,QAAQ,CAAC,IAAI,CAAC;MACvD,IAAI,CAAC9E,YAAY,CAACsD,GAAG,CAAC,gBAAgB,CAAC,EAAEsD,aAAa,EAAE;MACxD,IAAI,CAAC5C,sBAAsB,CAACc,QAAQ,CAAC,EAAE,CAAC;MACxC,IAAI,CAACe,kBAAkB,CAACa,kBAAkB,CAAC/B,EAAE,CAAC;MAE9CkC,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,6BAA6B,EAAEC,UAAU,EAAE;MAClD,CAAC,EAAE,CAAC,CAAC;IACP;EACF;EAEAC,2BAA2BA,CAACV,KAAmC;IAC7D,MAAMW,wBAAwB,GAAGX,KAAK,CAACE,MAAM,CAACC,SAAS;IACvD,MAAMS,oBAAoB,GAAG,IAAI,CAACpE,cAAc,CAAC2C,IAAI,CAClD0B,GAAG,IAAKA,GAAG,CAACpC,IAAI,KAAKkC,wBAAwB,CAC/C;IAED,IAAIC,oBAAoB,EAAE;MACxB,IAAI,CAAClH,YAAY,CACdsD,GAAG,CAAC,gBAAgB,CAAC,EACpBwB,QAAQ,CAACoC,oBAAoB,CAACvC,EAAE,CAAC;MACrC,IAAI,CAAC3E,YAAY,CAACsD,GAAG,CAAC,gBAAgB,CAAC,EAAEsD,aAAa,EAAE;MAExDC,UAAU,CAAC,MAAK;QACd,IAAI,CAACO,+BAA+B,EAAEL,UAAU,EAAE;MACpD,CAAC,EAAE,CAAC,CAAC;IACP;EACF;EAEQ7C,kBAAkBA,CAACvD,KAAa;IACtC,MAAM0G,WAAW,GAAG1G,KAAK,CAACgD,WAAW,EAAE;IACvC,OAAO,IAAI,CAACd,WAAW,CAACe,MAAM,CAAEiB,UAAU,IACxCA,UAAU,CAACE,IAAI,CAACpB,WAAW,EAAE,CAACG,QAAQ,CAACuD,WAAW,CAAC,CACpD;EACH;EAEQlB,qBAAqBA,CAACxF,KAAa;IACzC,MAAM0G,WAAW,GAAG1G,KAAK,CAACgD,WAAW,EAAE;IACvC,OAAO,IAAI,CAACb,cAAc,CAACc,MAAM,CAAEgB,YAAY,IAC7CA,YAAY,CAACG,IAAI,CAACpB,WAAW,EAAE,CAACG,QAAQ,CAACuD,WAAW,CAAC,CACtD;EACH;EAEAC,iBAAiBA,CAACC,cAAsB;IACtC,OAAOA,cAAc;EACvB;EAEAC,mBAAmBA,CAACC,gBAAwB;IAC1C,OAAOA,gBAAgB;EACzB;EAEAC,qBAAqBA,CAACtE,UAA+B;IACnD,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;MAClC,OAAOA,UAAU;IACnB;IACA,OAAOA,UAAU,GAAGA,UAAU,CAACS,QAAQ,GAAG,EAAE;EAC9C;EAEA8D,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACb,6BAA6B,EAAEc,SAAS,EAAE;MAClD,IAAI,CAAC7D,oBAAoB,CAACe,QAAQ,CAAC,EAAE,CAAC;MACtC,IAAI,CAAC9E,YAAY,CAACsD,GAAG,CAAC,cAAc,CAAC,EAAEsD,aAAa,EAAE;MACtDC,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,6BAA6B,EAAEe,SAAS,EAAE;MACjD,CAAC,EAAE,CAAC,CAAC;IACP;EACF;EAEAC,qBAAqBA,CAAA;IACnB,IACE,IAAI,CAAC/D,oBAAoB,CAACpD,KAAK,IAC/B,CAAC,IAAI,CAACyG,+BAA+B,EAAEQ,SAAS,EAChD;MACA,IAAI,CAAC5D,sBAAsB,CAACc,QAAQ,CAAC,EAAE,CAAC;MACxC,IAAI,CAAC9E,YAAY,CAACsD,GAAG,CAAC,gBAAgB,CAAC,EAAEsD,aAAa,EAAE;MACxDC,UAAU,CAAC,MAAK;QACd,IAAI,CAACO,+BAA+B,EAAES,SAAS,EAAE;MACnD,CAAC,EAAE,CAAC,CAAC;IACP;EACF;EAEME,WAAWA,CAACzB,KAA2B;IAAA,IAAA0B,KAAA;IAAA,OAAAC,iBAAA;MAC3C,IAAI,CAACD,KAAI,CAACjI,QAAQ,EAAE4E,EAAE,EAAE;MAExB,IAAI2B,KAAK,CAAC4B,OAAO,EAAE;QACjB,MAAMC,SAAS,SAASH,KAAI,CAACjJ,KAAK,CAACqJ,OAAO,CAAC,6BAA6B,CAAC;QACzE,IAAID,SAAS,EAAE;UACbH,KAAI,CAAClI,OAAO,CAACuI,IAAI,EAAE;UACnBL,KAAI,CAAC9I,eAAe,CACjBoJ,MAAM,CAACN,KAAI,CAACjI,QAAQ,CAAC4E,EAAE,EAAE;YAAElE,GAAG,EAAE;UAAI,CAAE,CAAC,CACvCgD,IAAI,CAACxH,QAAQ,CAAC,MAAM+L,KAAI,CAAClI,OAAO,CAACyI,IAAI,EAAE,CAAC,CAAC,CACzCpE,SAAS,CAAC;YACTiB,IAAI,EAAEA,CAAA,KAAK;cACT4C,KAAI,CAAChI,YAAY,CAACsD,GAAG,CAAC,KAAK,CAAC,EAAEwB,QAAQ,CAAC,IAAI,CAAC;cAC5CkD,KAAI,CAACjJ,KAAK,CAACyJ,OAAO,CAAC,uCAAuC,CAAC;YAC7D,CAAC;YACD1C,KAAK,EAAGA,KAAK,IAAI;cACfkC,KAAI,CAAChI,YAAY,CAACsD,GAAG,CAAC,KAAK,CAAC,EAAEwB,QAAQ,CAAC,KAAK,CAAC;cAC7CkD,KAAI,CAACjJ,KAAK,CAAC+G,KAAK,CACdA,KAAK,CAACA,KAAK,EAAEC,MAAM,IAAI,iCAAiC,CACzD;YACH;WACD,CAAC;QACN,CAAC,MAAM;UACLiC,KAAI,CAAChI,YAAY,CAACsD,GAAG,CAAC,KAAK,CAAC,EAAEwB,QAAQ,CAAC,KAAK,CAAC;QAC/C;MACF,CAAC,MAAM;QACL,MAAMqD,SAAS,SAASH,KAAI,CAACjJ,KAAK,CAACqJ,OAAO,CACxC,gCAAgC,CACjC;QACD,IAAID,SAAS,EAAE;UACbH,KAAI,CAAClI,OAAO,CAACuI,IAAI,EAAE;UACnBL,KAAI,CAAC9I,eAAe,CACjBoJ,MAAM,CAACN,KAAI,CAACjI,QAAQ,CAAC4E,EAAE,EAAE;YAAElE,GAAG,EAAE;UAAK,CAAE,CAAC,CACxCgD,IAAI,CAACxH,QAAQ,CAAC,MAAM+L,KAAI,CAAClI,OAAO,CAACyI,IAAI,EAAE,CAAC,CAAC,CACzCpE,SAAS,CAAC;YACTiB,IAAI,EAAEA,CAAA,KAAK;cACT4C,KAAI,CAAChI,YAAY,CAACsD,GAAG,CAAC,KAAK,CAAC,EAAEwB,QAAQ,CAAC,KAAK,CAAC;cAC7CkD,KAAI,CAACjJ,KAAK,CAACyJ,OAAO,CAAC,0CAA0C,CAAC;YAChE,CAAC;YACD1C,KAAK,EAAGA,KAAK,IAAI;cACfkC,KAAI,CAAChI,YAAY,CAACsD,GAAG,CAAC,KAAK,CAAC,EAAEwB,QAAQ,CAAC,IAAI,CAAC;cAC5CkD,KAAI,CAACjJ,KAAK,CAAC+G,KAAK,CACdA,KAAK,CAACA,KAAK,EAAEC,MAAM,IAAI,iCAAiC,CACzD;YACH;WACD,CAAC;QACN,CAAC,MAAM;UACLiC,KAAI,CAAChI,YAAY,CAACsD,GAAG,CAAC,KAAK,CAAC,EAAEwB,QAAQ,CAAC,IAAI,CAAC;QAC9C;MACF;IAAC;EACH;EAEM2D,iBAAiBA,CAACnC,KAA2B;IAAA,IAAAoC,MAAA;IAAA,OAAAT,iBAAA;MACjD,IAAI,CAACS,MAAI,CAAC3I,QAAQ,EAAE4E,EAAE,EAAE;MAExB,IAAI2B,KAAK,CAAC4B,OAAO,EAAE;QACjB,MAAMC,SAAS,SAASO,MAAI,CAAC3J,KAAK,CAACqJ,OAAO,CACxC,qDAAqD,CACtD;QACD,IAAID,SAAS,EAAE;UACbO,MAAI,CAAC5I,OAAO,CAACuI,IAAI,EAAE;UACnBK,MAAI,CAAC7I,aAAa,CACf8I,SAAS,CAAC,YAAY,CAAC,CACvBlF,IAAI,CACHrH,SAAS,CAAE4I,MAAM,IACf0D,MAAI,CAACxJ,eAAe,CAACoJ,MAAM,CAACI,MAAI,CAAC3I,QAAS,CAAC4E,EAAE,EAAE;YAC7CiE,QAAQ,EAAE5D,MAAM,CAACL;WAClB,CAAC,CACH,EACD1I,QAAQ,CAAC,MAAMyM,MAAI,CAAC5I,OAAO,CAACyI,IAAI,EAAE,CAAC,CACpC,CACApE,SAAS,CAAC;YACTiB,IAAI,EAAEA,CAAA,KAAK;cACTsD,MAAI,CAAC1I,YAAY,CAACsD,GAAG,CAAC,WAAW,CAAC,EAAEwB,QAAQ,CAAC,IAAI,CAAC;cAClD4D,MAAI,CAACxD,oBAAoB,CAAC,KAAK,CAAC;cAChCwD,MAAI,CAAC3J,KAAK,CAACyJ,OAAO,CAChB,8CAA8C,CAC/C;cACDE,MAAI,CAACvG,wBAAwB,CAAC0G,IAAI,CAAC,IAAI,CAAC;YAC1C,CAAC;YACD/C,KAAK,EAAGA,KAAK,IAAI;cACf4C,MAAI,CAAC1I,YAAY,CAACsD,GAAG,CAAC,WAAW,CAAC,EAAEwB,QAAQ,CAAC,KAAK,CAAC;cACnD4D,MAAI,CAAC3J,KAAK,CAAC+G,KAAK,CACdA,KAAK,CAACA,KAAK,EAAEC,MAAM,IACjB,4CAA4C,CAC/C;YACH;WACD,CAAC;QACN,CAAC,MAAM;UACL2C,MAAI,CAAC1I,YAAY,CAACsD,GAAG,CAAC,WAAW,CAAC,EAAEwB,QAAQ,CAAC,KAAK,CAAC;QACrD;MACF,CAAC,MAAM;QACL,MAAMqD,SAAS,SAASO,MAAI,CAAC3J,KAAK,CAACqJ,OAAO,CACxC,wDAAwD,CACzD;QACD,IAAID,SAAS,EAAE;UACbO,MAAI,CAAC5I,OAAO,CAACuI,IAAI,EAAE;UACnBK,MAAI,CAAC7I,aAAa,CACf8I,SAAS,CAAC,cAAc,CAAC,CACzBlF,IAAI,CACHrH,SAAS,CAAE4I,MAAM,IACf0D,MAAI,CAACxJ,eAAe,CAACoJ,MAAM,CAACI,MAAI,CAAC3I,QAAS,CAAC4E,EAAE,EAAE;YAC7CiE,QAAQ,EAAE5D,MAAM,CAACL;WAClB,CAAC,CACH,EACD1I,QAAQ,CAAC,MAAMyM,MAAI,CAAC5I,OAAO,CAACyI,IAAI,EAAE,CAAC,CACpC,CACApE,SAAS,CAAC;YACTiB,IAAI,EAAEA,CAAA,KAAK;cACTsD,MAAI,CAAC1I,YAAY,CAACsD,GAAG,CAAC,WAAW,CAAC,EAAEwB,QAAQ,CAAC,KAAK,CAAC;cACnD4D,MAAI,CAACxD,oBAAoB,CAAC,IAAI,CAAC;cAC/BwD,MAAI,CAAC3J,KAAK,CAACyJ,OAAO,CAChB,gDAAgD,CACjD;cACDE,MAAI,CAACvG,wBAAwB,CAAC0G,IAAI,CAAC,KAAK,CAAC;YAC3C,CAAC;YACD/C,KAAK,EAAGA,KAAK,IAAI;cACf4C,MAAI,CAAC1I,YAAY,CAACsD,GAAG,CAAC,WAAW,CAAC,EAAEwB,QAAQ,CAAC,IAAI,CAAC;cAClD4D,MAAI,CAAC3J,KAAK,CAAC+G,KAAK,CACdA,KAAK,CAACA,KAAK,EAAEC,MAAM,IACjB,4CAA4C,CAC/C;YACH;WACD,CAAC;QACN,CAAC,MAAM;UACL2C,MAAI,CAAC1I,YAAY,CAACsD,GAAG,CAAC,WAAW,CAAC,EAAEwB,QAAQ,CAAC,IAAI,CAAC;QACpD;MACF;IAAC;EACH;EAEQI,oBAAoBA,CAACd,MAAe;IAC1C,MAAM0E,gBAAgB,GAAG,CAAC,WAAW,CAAC;IAEtCC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChJ,YAAY,CAACiJ,QAAQ,CAAC,CAACC,OAAO,CAAEC,WAAW,IAAI;MAC9D,IAAI,CAACL,gBAAgB,CAAChF,QAAQ,CAACqF,WAAW,CAAC,EAAE;QAC3C,MAAMC,OAAO,GAAG,IAAI,CAACpJ,YAAY,CAACsD,GAAG,CAAC6F,WAAW,CAAC;QAClD,IAAI/E,MAAM,EAAE;UACVgF,OAAO,EAAEhF,MAAM,EAAE;QACnB,CAAC,MAAM;UACLgF,OAAO,EAAE/E,OAAO,EAAE;QACpB;MACF;IACF,CAAC,CAAC;EACJ;EAEQG,6BAA6BA,CAAA;IACnC,IAAI,CAACxE,YAAY,CACdsD,GAAG,CAAC,gBAAgB,CAAC,EACpBE,YAAY,CAACC,IAAI,CACjBzH,YAAY,CAAC,GAAG,CAAC,EACjBI,SAAS,CAAE8D,cAAc,IAAI;MAC3B,MAAMmJ,cAAc,GAAG,IAAI,CAACrJ,YAAY,CAACsD,GAAG,CAAC,cAAc,CAAC,EAAE3C,KAAK;MACnE,IAAIT,cAAc,IAAImJ,cAAc,EAAE;QACpC,OAAO,IAAI,CAACnK,eAAe,CAACoK,sBAAsB,CAChDpJ,cAAc,EACdmJ,cAAc,CACf;MACH;MACA,OAAOtN,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CACAoI,SAAS,CAAEoF,OAAO,IAAI;MACrB,IAAI,CAACA,OAAO,EAAE;QACZ,IAAI,CAACvJ,YAAY,CACdsD,GAAG,CAAC,gBAAgB,CAAC,EACpBkG,SAAS,CAAC;UAAEC,iBAAiB,EAAE;QAAI,CAAE,CAAC;MAC5C;IACF,CAAC,CAAC;IAEJ,IAAI,CAACzJ,YAAY,CAACsD,GAAG,CAAC,cAAc,CAAC,EAAEE,YAAY,CAACW,SAAS,CAAC,MAAK;MACjE,IAAI,CAACnE,YAAY,CAACsD,GAAG,CAAC,gBAAgB,CAAC,EAAEoG,sBAAsB,EAAE;IACnE,CAAC,CAAC;EACJ;EAEAC,oBAAoBA,CAACrD,KAAmC;IACtD,MAAMlD,UAAU,GAAGkD,KAAK,CAACE,MAAM,CAAC7F,KAAmB;IACnD,IAAI,CAACyC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACpD,YAAY,CAAC0E,UAAU,CAAC;MAC3B9C,YAAY,EAAEwB,UAAU,CAACuB,EAAE;MAC3B9C,kBAAkB,EAAEuB,UAAU,CAACS;KAChC,CAAC;EACJ;EAEA+F,eAAeA,CAAA;IACb,IAAI,CAACxG,UAAU,GAAG,IAAI;IACtB,IAAI,CAACpD,YAAY,CAAC0E,UAAU,CAAC;MAC3B9C,YAAY,EAAE,IAAI;MAClBC,kBAAkB,EAAE;KACrB,CAAC;EACJ;EAEQ4C,uBAAuBA,CAAA;IAC7B,MAAMhD,sBAAsB,GAAG,IAAI,CAACzB,YAAY,CAACsD,GAAG,CAClD,wBAAwB,CACzB;IACD,MAAM5B,cAAc,GAAG,IAAI,CAAC1B,YAAY,CAACsD,GAAG,CAAC,gBAAgB,CAAC;IAC9D,MAAM3B,cAAc,GAAG,IAAI,CAAC3B,YAAY,CAACsD,GAAG,CAAC,gBAAgB,CAAC;IAE9D,MAAMuG,gBAAgB,GAAIC,WAAoB,IAAI;MAChD,IAAIA,WAAW,EAAE;QACfrI,sBAAsB,EAAEsI,aAAa,CAAC,CAAChN,UAAU,CAACoD,QAAQ,CAAC,CAAC;QAC5DuB,cAAc,EAAEqI,aAAa,CAAC,CAAChN,UAAU,CAACoD,QAAQ,CAAC,CAAC;QACpDwB,cAAc,EAAEoI,aAAa,CAAC,CAAChN,UAAU,CAACoD,QAAQ,CAAC,CAAC;MACtD,CAAC,MAAM;QACLsB,sBAAsB,EAAEsI,aAAa,CAAC,IAAI,CAAC;QAC3CrI,cAAc,EAAEqI,aAAa,CAAC,IAAI,CAAC;QACnCpI,cAAc,EAAEoI,aAAa,CAAC,IAAI,CAAC;QAEnCtI,sBAAsB,EAAEiD,UAAU,CAAC,IAAI,EAAE;UAAEsF,SAAS,EAAE;QAAK,CAAE,CAAC;QAC9DtI,cAAc,EAAEgD,UAAU,CAAC,IAAI,EAAE;UAAEsF,SAAS,EAAE;QAAK,CAAE,CAAC;QACtDrI,cAAc,EAAE+C,UAAU,CAAC,IAAI,EAAE;UAAEsF,SAAS,EAAE;QAAK,CAAE,CAAC;MACxD;MAEAvI,sBAAsB,EAAEwI,eAAe,EAAE;MACzCvI,cAAc,EAAEuI,eAAe,EAAE;MACjCtI,cAAc,EAAEsI,eAAe,EAAE;MAEjCxI,sBAAsB,EAAEiI,sBAAsB,CAAC;QAAEM,SAAS,EAAE;MAAK,CAAE,CAAC;MACpEtI,cAAc,EAAEgI,sBAAsB,CAAC;QAAEM,SAAS,EAAE;MAAK,CAAE,CAAC;MAC5DrI,cAAc,EAAE+H,sBAAsB,CAAC;QAAEM,SAAS,EAAE;MAAK,CAAE,CAAC;IAC9D,CAAC;IAED,MAAME,eAAe,GAAG,IAAI,CAAClK,YAAY,CAACsD,GAAG,CAAC,UAAU,CAAC,EAAE3C,KAAK,IAAI,KAAK;IACzEkJ,gBAAgB,CAACK,eAAe,CAAC;IAEjC,IAAI,CAAClK,YAAY,CAACsD,GAAG,CAAC,UAAU,CAAC,EAAEE,YAAY,CAACW,SAAS,CAAC0F,gBAAgB,CAAC;EAC7E;EAEAN,OAAOA,CAAA;IACL,OAAO,IAAI,CAACvJ,YAAY,CAACmK,KAAK;EAChC;EAEAC,QAAQA,CAAA;IACNrB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChJ,YAAY,CAACiJ,QAAQ,CAAC,CAACC,OAAO,CAAEmB,GAAG,IAAI;MACtD,MAAMjB,OAAO,GAAG,IAAI,CAACpJ,YAAY,CAACsD,GAAG,CAAC+G,GAAG,CAAC;MAC1C,IAAIjB,OAAO,EAAE;QACXA,OAAO,CAACxC,aAAa,EAAE;MACzB;IACF,CAAC,CAAC;IAEF,OAAO,IAAI,CAAC5G,YAAY,CAACsK,WAAW,EAAE;EACxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;cA9mBC3N,SAAS;QAAA4N,IAAA,GAAC,gBAAgB;MAAA;;cAC1B5N,SAAS;QAAA4N,IAAA,GAAC,iBAAiB,EAAE;UAAEC,IAAI,EAAEtN;QAAsB,CAAE;MAAA;;cAE7DP,SAAS;QAAA4N,IAAA,GAAC,kBAAkB;MAAA;;cAC5B5N,SAAS;QAAA4N,IAAA,GAAC,mBAAmB,EAAE;UAAEC,IAAI,EAAEtN;QAAsB,CAAE;MAAA;;cAG/DT;MAAK;;cAgCLC;MAAM;;cACNA;MAAM;;;;AAzCIgC,2BAA2B,GAAA+L,UAAA,EAnBvClO,SAAS,CAAC;EACTmO,QAAQ,EAAE,0BAA0B;EACpCC,QAAA,EAAAC,oBAAoD;EAEpDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPhO,mBAAmB,EACnBO,kBAAkB,EAClBE,cAAc,EACdC,eAAe,EACfR,oBAAoB,EACpBC,qBAAqB,EACrBK,aAAa,EACbF,eAAe,EACfD,mBAAmB,EACnBb,SAAS,EACT6B,oBAAoB,CACrB;;CACF,CAAC,C,EACWO,2BAA2B,CAgnBvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}